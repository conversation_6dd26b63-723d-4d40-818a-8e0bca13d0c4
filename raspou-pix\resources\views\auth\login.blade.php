@extends('layouts.app')

@section('title', 'Entrar - Ra<PERSON>ou Pix')

@section('content')
<div class="max-w-md mx-auto">
    <div class="bg-gray-800 p-8 rounded-lg shadow-lg">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                🎰 Entrar
            </h1>
            <p class="text-gray-300 mt-2">Acesse sua conta e comece a jogar!</p>
        </div>

        @if ($errors->any())
            <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
                <ul class="list-disc list-inside">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @if (session('success'))
            <div class="bg-green-600 text-white p-4 rounded-lg mb-6">
                {{ session('success') }}
            </div>
        @endif

        <form method="POST" action="{{ route('login.post') }}">
            @csrf

            <div class="mb-6">
                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                    E-mail
                </label>
                <input type="email"
                       id="email"
                       name="email"
                       value="{{ old('email') }}"
                       required
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                       placeholder="<EMAIL>">
            </div>

            <div class="mb-6">
                <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                    Senha
                </label>
                <input type="password"
                       id="password"
                       name="password"
                       required
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                       placeholder="Sua senha">
            </div>

            <div class="flex items-center justify-between mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="remember" class="rounded border-gray-600 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm text-gray-300">Lembrar de mim</span>
                </label>
                <a href="#" class="text-sm text-purple-400 hover:text-purple-300">
                    Esqueceu a senha?
                </a>
            </div>

            <button type="submit" class="w-full btn-primary py-3 rounded-lg text-white font-semibold text-lg">
                <i class="fas fa-sign-in-alt mr-2"></i>Entrar
            </button>
        </form>

        <div class="text-center mt-6">
            <p class="text-gray-300">
                Não tem uma conta?
                <a href="{{ route('register') }}" class="text-purple-400 hover:text-purple-300 font-semibold">
                    Cadastre-se grátis
                </a>
            </p>
        </div>
    </div>
</div>
@endsection
