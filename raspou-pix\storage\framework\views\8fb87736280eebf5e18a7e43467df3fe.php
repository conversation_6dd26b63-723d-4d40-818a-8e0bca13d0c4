<?php $__env->startSection('title', '<PERSON><PERSON><PERSON><PERSON><PERSON>í<PERSON> - <PERSON><PERSON><PERSON> Pix'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-b from-green-400 to-green-600 p-4">
    <!-- Header com saldo -->
    <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
            <img src="https://files.raspoupix.com/logo-white.png" alt="Raspou Pix" class="h-8 mr-3">
        </div>
        <?php if(auth()->guard()->check()): ?>
        <div class="flex items-center space-x-4">
            <div class="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
                <span class="text-sm mr-2">R$</span>
                <span class="font-bold"><?php echo e(number_format(auth()->user()->balance, 2, ',', '.')); ?></span>
            </div>
            <button class="bg-green-700 hover:bg-green-800 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>Depositar
            </button>
        </div>
        <?php endif; ?>
    </div>

    <!-- Título da página -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">Raspadinhas</h1>
    </div>

    <!-- Últimos Ganhadores -->
    <div class="max-w-4xl mx-auto mb-8">
        <div class="bg-green-600 rounded-t-lg p-4">
            <h2 class="text-white font-bold text-xl text-center">ÚLTIMOS GANHADORES</h2>
        </div>

        <div class="bg-white rounded-b-lg p-4 space-y-4">
            <?php
                // Simulando alguns ganhadores para demonstração
                $winners = [
                    ['name' => 'Juliana B.', 'city' => 'Porto Alegre, RS', 'prize' => 100.00, 'date' => '31/07/2025'],
                    ['name' => 'Lucas R.', 'city' => 'Fortaleza, CE', 'prize' => 2200.00, 'date' => '31/07/2025'],
                    ['name' => 'Daniela T.', 'city' => 'Recife, PE', 'prize' => 2200.00, 'date' => '31/07/2025'],
                ];
            ?>

            <?php $__currentLoopData = $winners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $winner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-blue-900 rounded-lg p-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                        <span class="text-white font-bold"><?php echo e(substr($winner['name'], 0, 1)); ?></span>
                    </div>
                    <div>
                        <div class="flex items-center mb-1">
                            <span class="text-white font-bold mr-2">Ganhador(a)</span>
                            <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">🏆 Raspadinha</span>
                        </div>
                        <p class="text-white font-bold"><?php echo e($winner['name']); ?></p>
                        <p class="text-blue-300 text-sm"><?php echo e($winner['city']); ?></p>
                        <p class="text-white font-bold">Prêmio</p>
                        <p class="text-green-400 font-bold text-lg">R$ <?php echo e(number_format($winner['prize'], 2, ',', '.')); ?></p>
                        <p class="text-blue-300 text-xs">📅 <?php echo e($winner['date']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <!-- Scratch Cards Grid -->
    <?php if($scratchCards->count() > 0): ?>
        <div class="max-w-4xl mx-auto space-y-6">
            <?php $__currentLoopData = $scratchCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-blue-900 rounded-lg overflow-hidden shadow-lg">
                <!-- Header da raspadinha -->
                <div class="relative h-48 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                    <!-- Badge de preço -->
                    <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full font-bold">
                        <?php echo e($card->formatted_price); ?>

                    </div>

                    <!-- Imagem/Ícone central -->
                    <div class="text-center">
                        <div class="text-6xl mb-2">🎰</div>
                        <h2 class="text-2xl font-bold text-white"><?php echo e(strtoupper($card->name)); ?></h2>
                        <p class="text-yellow-300 font-bold text-lg">ACHE 3 IGUAIS</p>
                        <p class="text-yellow-300 font-bold">🎯 GANHE NA HORA!</p>
                    </div>

                    <!-- Elementos decorativos -->
                    <div class="absolute top-4 left-4">
                        <div class="w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center">
                            <span class="text-2xl">💰</span>
                        </div>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-xl">🎁</span>
                        </div>
                    </div>
                </div>

                <!-- Informações da raspadinha -->
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <p class="text-white font-bold text-lg">
                                Prêmios até R$ <?php echo e(number_format(collect($card->prize_structure)->max('amount'), 2, ',', '.')); ?> NO PIX
                            </p>
                            <p class="text-blue-300 text-sm"><?php echo e($card->description); ?></p>
                            <p class="text-red-400 font-bold">
                                Prêmio até: R$ <?php echo e(number_format(collect($card->prize_structure)->max('amount'), 2, ',', '.')); ?>

                            </p>
                        </div>
                    </div>

                    <?php if($card->available_cards > 0): ?>
                        <?php if(auth()->guard()->check()): ?>
                            <?php if(auth()->user()->balance >= $card->price): ?>
                                <button onclick="purchaseCard(<?php echo e($card->id); ?>)"
                                        class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-6 rounded-lg transition-colors text-lg">
                                    JOGAR AGORA →
                                </button>
                            <?php else: ?>
                                <button disabled class="w-full bg-gray-600 text-gray-400 font-bold py-4 px-6 rounded-lg cursor-not-allowed text-lg">
                                    SALDO INSUFICIENTE
                                </button>
                            <?php endif; ?>
                        <?php else: ?>
                            <a href="<?php echo e(route('login')); ?>"
                               class="block w-full bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-6 rounded-lg transition-colors text-lg text-center">
                                ENTRAR PARA JOGAR →
                            </a>
                        <?php endif; ?>
                    <?php else: ?>
                        <button disabled class="w-full bg-gray-600 text-white font-bold py-4 px-6 rounded-lg cursor-not-allowed text-lg">
                            ESGOTADO
                        </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>


        <!-- Call to Action -->
        <div class="max-w-4xl mx-auto mt-12 bg-blue-900 rounded-lg p-8 text-center">
            <h2 class="text-2xl font-bold text-white mb-4">Pronto para Ganhar?</h2>
            <p class="text-blue-300 mb-6">
                Junte-se a milhares de jogadores que já estão se divertindo e ganhando prêmios em nossa plataforma.
            </p>
            <?php if(auth()->guard()->guest()): ?>
            <a href="<?php echo e(route('register')); ?>" class="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-8 rounded-lg text-lg transition-colors">
                JOGAR AGORA
            </a>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <div class="flex justify-center mt-8">
            <?php echo e($scratchCards->links()); ?>

        </div>
    <?php else: ?>
        <div class="text-center py-16">
            <div class="text-6xl mb-4">😔</div>
            <h2 class="text-2xl font-bold text-white mb-4">Nenhuma raspadinha disponível</h2>
            <p class="text-blue-200 mb-8">No momento não temos raspadinhas disponíveis. Volte em breve!</p>
            <a href="<?php echo e(route('home')); ?>" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg text-white font-semibold transition-colors">
                <i class="fas fa-home mr-2"></i>Voltar ao Início
            </a>
        </div>
    <?php endif; ?>

    <!-- Bottom Navigation (Mobile) -->
    <div class="fixed bottom-0 left-0 right-0 bg-green-600 p-4 md:hidden">
        <div class="flex justify-around items-center text-white">
            <a href="<?php echo e(route('home')); ?>" class="flex flex-col items-center">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">Início</span>
            </a>
            <a href="<?php echo e(route('scratch-cards.index')); ?>" class="flex flex-col items-center">
                <i class="fas fa-ticket-alt text-xl mb-1"></i>
                <span class="text-xs">Raspadinhas</span>
            </a>
            <a href="<?php echo e(route('lucky-numbers')); ?>" class="flex flex-col items-center">
                <i class="fas fa-hashtag text-xl mb-1"></i>
                <span class="text-xs">Números</span>
            </a>
            <a href="<?php echo e(route('wallet')); ?>" class="flex flex-col items-center">
                <i class="fas fa-wallet text-xl mb-1"></i>
                <span class="text-xs">Carteira</span>
            </a>
            <a href="<?php echo e(route('profile')); ?>" class="flex flex-col items-center">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">Perfil</span>
            </a>
        </div>
    </div>
</div>

<?php if(auth()->guard()->check()): ?>
<?php $__env->startPush('scripts'); ?>
<script>
function purchaseCard(cardId) {
    if (confirm('Deseja comprar esta raspadinha?')) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Comprando...';
        button.disabled = true;

        $.post(`/games/purchase/${cardId}`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
        .done(function(response) {
            if (response.success) {
                window.location.href = response.redirect;
            } else {
                alert(response.message);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .fail(function() {
            alert('Erro ao processar compra. Tente novamente.');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\raspou-pix\resources\views/scratch-cards/index.blade.php ENDPATH**/ ?>