<?php $__env->startSection('title', 'Raspadinhas - RASPOU PIX'); ?>

<?php $__env->startSection('content'); ?>
<div data-v-5d31a7ef="" class="min-h-screen bg-base">
    <!-- Header com saldo -->
    <div data-v-5d31a7ef="" class="flex justify-between items-center p-4 navtop-color">
        <div data-v-5d31a7ef="" class="flex items-center">
            <img src="https://files.raspoupix.com/logo-white.png" alt="Raspou Pix" class="h-8 mr-3">
        </div>
        <?php if(auth()->guard()->check()): ?>
        <div data-v-5d31a7ef="" class="flex items-center space-x-4">
            <div data-v-5d31a7ef="" class="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
                <span data-v-5d31a7ef="" class="text-sm mr-2">R$</span>
                <span data-v-5d31a7ef="" class="font-bold"><?php echo e(number_format(auth()->user()->balance, 2, ',', '.')); ?></span>
            </div>
            <button data-v-5d31a7ef="" class="bg-green-700 hover:bg-green-800 text-white px-4 py-2 rounded-lg transition-colors">
                <i data-v-5d31a7ef="" class="fas fa-plus mr-2"></i>Depositar
            </button>
        </div>
        <?php endif; ?>
    </div>

    <!-- Últimos Ganhadores -->
    <div data-v-5d31a7ef="" class="p-4">
        <div data-v-5d31a7ef="" class="bg-green-600 rounded-t-lg p-4">
            <h2 data-v-5d31a7ef="" class="text-white font-bold text-xl text-center">ÚLTIMOS GANHADORES</h2>
        </div>
        
        <div data-v-5d31a7ef="" class="bg-white rounded-b-lg p-4 space-y-4">
            <?php
                // Simulando alguns ganhadores para demonstração
                $winners = [
                    ['name' => 'Juliana B.', 'city' => 'Porto Alegre, RS', 'prize' => 100.00, 'date' => '31/07/2025', 'avatar' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/100_reais.jpg'],
                    ['name' => 'Lucas R.', 'city' => 'Fortaleza, CE', 'prize' => 2200.00, 'date' => '31/07/2025', 'avatar' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/fone_bluetooth_c2050b7a.webp'],
                    ['name' => 'Daniela T.', 'city' => 'Recife, PE', 'prize' => 2200.00, 'date' => '31/07/2025', 'avatar' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/fone_bluetooth_c2050b7a.webp'],
                ];
            ?>
            
            <?php $__currentLoopData = $winners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $winner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div data-v-5d31a7ef="" class="bg-blue-900 rounded-lg p-4 flex items-center justify-between">
                <div data-v-5d31a7ef="" class="flex items-center">
                    <div data-v-5d31a7ef="" class="w-12 h-12 rounded-lg overflow-hidden mr-4">
                        <img data-v-5d31a7ef="" src="<?php echo e($winner['avatar']); ?>" alt="Avatar" class="w-full h-full object-cover">
                    </div>
                    <div data-v-5d31a7ef="">
                        <div data-v-5d31a7ef="" class="flex items-center mb-1">
                            <span data-v-5d31a7ef="" class="text-white font-bold mr-2">Ganhador(a)</span>
                            <span data-v-5d31a7ef="" class="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">🏆 Raspadinha</span>
                        </div>
                        <p data-v-5d31a7ef="" class="text-white font-bold"><?php echo e($winner['name']); ?></p>
                        <p data-v-5d31a7ef="" class="text-blue-300 text-sm"><?php echo e($winner['city']); ?></p>
                        <p data-v-5d31a7ef="" class="text-white font-bold">Prêmio</p>
                        <p data-v-5d31a7ef="" class="text-green-400 font-bold text-lg">R$ <?php echo e(number_format($winner['prize'], 2, ',', '.')); ?></p>
                        <p data-v-5d31a7ef="" class="text-blue-300 text-xs">📅 <?php echo e($winner['date']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <!-- Scratch Cards Grid -->
    <?php if($scratchCards->count() > 0): ?>
        <div data-v-5d31a7ef="" class="p-4 space-y-6">
            <?php $__currentLoopData = $scratchCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div data-v-5d31a7ef="" class="bg-blue-900 rounded-lg overflow-hidden shadow-lg">
                <!-- Header da raspadinha -->
                <div data-v-5d31a7ef="" class="relative h-48 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                    <!-- Badge de preço -->
                    <div data-v-5d31a7ef="" class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full font-bold">
                        <?php echo e($card->formatted_price); ?>

                    </div>
                    
                    <!-- Imagem/Ícone central -->
                    <div data-v-5d31a7ef="" class="text-center">
                        <div data-v-5d31a7ef="" class="text-6xl mb-2">🎰</div>
                        <h2 data-v-5d31a7ef="" class="text-2xl font-bold text-white">ACHE 3 IGUAIS</h2>
                        <p data-v-5d31a7ef="" class="text-yellow-300 font-bold text-lg">E GANHE NA HORA!</p>
                    </div>
                    
                    <!-- Elementos decorativos -->
                    <div data-v-5d31a7ef="" class="absolute top-4 left-4">
                        <div data-v-5d31a7ef="" class="w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center">
                            <span data-v-5d31a7ef="" class="text-2xl">💰</span>
                        </div>
                    </div>
                    <div data-v-5d31a7ef="" class="absolute bottom-4 right-4">
                        <div data-v-5d31a7ef="" class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <span data-v-5d31a7ef="" class="text-xl">🎁</span>
                        </div>
                    </div>
                </div>
                
                <!-- Informações da raspadinha -->
                <div data-v-5d31a7ef="" class="p-6">
                    <div data-v-5d31a7ef="" class="mb-4">
                        <p data-v-5d31a7ef="" class="text-white font-bold text-lg">
                            Prêmios até R$ <?php echo e(number_format(collect($card->prize_structure)->max('amount'), 2, ',', '.')); ?> NO PIX
                        </p>
                        <p data-v-5d31a7ef="" class="text-blue-300 text-sm"><?php echo e($card->description); ?></p>
                        <p data-v-5d31a7ef="" class="text-red-400 font-bold">
                            Prêmio até: R$ <?php echo e(number_format(collect($card->prize_structure)->max('amount'), 2, ',', '.')); ?>

                        </p>
                    </div>
                    
                    <?php if($card->available_cards > 0): ?>
                        <?php if(auth()->guard()->check()): ?>
                            <?php if(auth()->user()->balance >= $card->price): ?>
                                <button data-v-5d31a7ef="" onclick="purchaseCard(<?php echo e($card->id); ?>)" 
                                        class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-6 rounded-lg transition-colors text-lg">
                                    JOGAR AGORA →
                                </button>
                            <?php else: ?>
                                <button data-v-5d31a7ef="" disabled class="w-full bg-gray-600 text-gray-400 font-bold py-4 px-6 rounded-lg cursor-not-allowed text-lg">
                                    SALDO INSUFICIENTE
                                </button>
                            <?php endif; ?>
                        <?php else: ?>
                            <a data-v-5d31a7ef="" href="<?php echo e(route('login')); ?>" 
                               class="block w-full bg-red-600 hover:bg-red-700 text-white font-bold py-4 px-6 rounded-lg transition-colors text-lg text-center">
                                ENTRAR PARA JOGAR →
                            </a>
                        <?php endif; ?>
                    <?php else: ?>
                        <button data-v-5d31a7ef="" disabled class="w-full bg-gray-600 text-white font-bold py-4 px-6 rounded-lg cursor-not-allowed text-lg">
                            ESGOTADO
                        </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php else: ?>
        <div data-v-5d31a7ef="" class="text-center py-16">
            <div data-v-5d31a7ef="" class="text-6xl mb-4">😔</div>
            <h2 data-v-5d31a7ef="" class="text-2xl font-bold text-white mb-4">Nenhuma raspadinha disponível</h2>
            <p data-v-5d31a7ef="" class="text-blue-200 mb-8">No momento não temos raspadinhas disponíveis. Volte em breve!</p>
            <a data-v-5d31a7ef="" href="<?php echo e(route('home')); ?>" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg text-white font-semibold transition-colors">
                <i data-v-5d31a7ef="" class="fas fa-home mr-2"></i>Voltar ao Início
            </a>
        </div>
    <?php endif; ?>
    
    <!-- Bottom Navigation (Mobile) -->
    <div data-v-5d31a7ef="" class="fixed bottom-0 left-0 right-0 bg-green-600 p-4 md:hidden">
        <div data-v-5d31a7ef="" class="flex justify-around items-center text-white">
            <a data-v-5d31a7ef="" href="<?php echo e(route('home')); ?>" class="flex flex-col items-center">
                <i data-v-5d31a7ef="" class="fas fa-home text-xl mb-1"></i>
                <span data-v-5d31a7ef="" class="text-xs">Início</span>
            </a>
            <a data-v-5d31a7ef="" href="<?php echo e(route('scratch-cards.index')); ?>" class="flex flex-col items-center">
                <i data-v-5d31a7ef="" class="fas fa-ticket-alt text-xl mb-1"></i>
                <span data-v-5d31a7ef="" class="text-xs">Raspadinhas</span>
            </a>
            <a data-v-5d31a7ef="" href="<?php echo e(route('lucky-numbers')); ?>" class="flex flex-col items-center">
                <i data-v-5d31a7ef="" class="fas fa-hashtag text-xl mb-1"></i>
                <span data-v-5d31a7ef="" class="text-xs">Números</span>
            </a>
            <a data-v-5d31a7ef="" href="<?php echo e(route('wallet')); ?>" class="flex flex-col items-center">
                <i data-v-5d31a7ef="" class="fas fa-wallet text-xl mb-1"></i>
                <span data-v-5d31a7ef="" class="text-xs">Carteira</span>
            </a>
            <a data-v-5d31a7ef="" href="<?php echo e(route('profile')); ?>" class="flex flex-col items-center">
                <i data-v-5d31a7ef="" class="fas fa-user text-xl mb-1"></i>
                <span data-v-5d31a7ef="" class="text-xs">Perfil</span>
            </a>
        </div>
    </div>
</div>

<?php if(auth()->guard()->check()): ?>
<?php $__env->startPush('scripts'); ?>
<script>
function purchaseCard(cardId) {
    if (confirm('Deseja comprar esta raspadinha?')) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Comprando...';
        button.disabled = true;
        
        fetch(`/games/purchase/${cardId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.redirect;
            } else {
                alert(data.message);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            alert('Erro ao processar compra. Tente novamente.');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\raspou-pix\resources\views/scratch-cards/index.blade.php ENDPATH**/ ?>