<?php

namespace Database\Seeders;

use App\Models\ScratchCard;
use Illuminate\Database\Seeder;

class ScratchCardSeeder extends Seeder
{
    public function run(): void
    {
        $scratchCards = [
            [
                'name' => 'Sorte Dourada',
                'description' => 'Raspadinha clássica com prêmios de até R$ 1.000! Três símbolos iguais e você ganha!',
                'price' => 5.00,
                'prize_structure' => [
                    ['amount' => 1000.00, 'weight' => 1],    // 0.1% chance
                    ['amount' => 500.00, 'weight' => 2],     // 0.2% chance
                    ['amount' => 100.00, 'weight' => 5],     // 0.5% chance
                    ['amount' => 50.00, 'weight' => 10],     // 1% chance
                    ['amount' => 25.00, 'weight' => 20],     // 2% chance
                    ['amount' => 10.00, 'weight' => 50],     // 5% chance
                    ['amount' => 5.00, 'weight' => 100],     // 10% chance
                    ['amount' => 0, 'weight' => 812],        // 81.2% chance (no prize)
                ],
                'total_cards' => 1000,
                'is_active' => true,
            ],
            [
                'name' => 'Mega Fortuna',
                'description' => 'A raspadinha mais emocionante! Prêmios incríveis de até R$ 5.000 te esperando!',
                'price' => 10.00,
                'prize_structure' => [
                    ['amount' => 5000.00, 'weight' => 1],    // 0.1% chance
                    ['amount' => 2000.00, 'weight' => 2],    // 0.2% chance
                    ['amount' => 1000.00, 'weight' => 3],    // 0.3% chance
                    ['amount' => 500.00, 'weight' => 5],     // 0.5% chance
                    ['amount' => 200.00, 'weight' => 10],    // 1% chance
                    ['amount' => 100.00, 'weight' => 20],    // 2% chance
                    ['amount' => 50.00, 'weight' => 30],     // 3% chance
                    ['amount' => 20.00, 'weight' => 50],     // 5% chance
                    ['amount' => 10.00, 'weight' => 80],     // 8% chance
                    ['amount' => 0, 'weight' => 799],        // 79.9% chance (no prize)
                ],
                'total_cards' => 500,
                'is_active' => true,
            ],
            [
                'name' => 'Pix Rápido',
                'description' => 'Raspadinha econômica perfeita para começar! Diversão garantida com prêmios de até R$ 200!',
                'price' => 2.00,
                'prize_structure' => [
                    ['amount' => 200.00, 'weight' => 2],     // 0.2% chance
                    ['amount' => 100.00, 'weight' => 5],     // 0.5% chance
                    ['amount' => 50.00, 'weight' => 10],     // 1% chance
                    ['amount' => 20.00, 'weight' => 25],     // 2.5% chance
                    ['amount' => 10.00, 'weight' => 50],     // 5% chance
                    ['amount' => 5.00, 'weight' => 80],      // 8% chance
                    ['amount' => 2.00, 'weight' => 100],     // 10% chance
                    ['amount' => 0, 'weight' => 728],        // 72.8% chance (no prize)
                ],
                'total_cards' => 2000,
                'is_active' => true,
            ],
            [
                'name' => 'Diamante Real',
                'description' => 'Para os verdadeiros apostadores! Prêmios exclusivos de até R$ 10.000!',
                'price' => 25.00,
                'prize_structure' => [
                    ['amount' => 10000.00, 'weight' => 1],   // 0.1% chance
                    ['amount' => 5000.00, 'weight' => 2],    // 0.2% chance
                    ['amount' => 2500.00, 'weight' => 3],    // 0.3% chance
                    ['amount' => 1000.00, 'weight' => 5],    // 0.5% chance
                    ['amount' => 500.00, 'weight' => 10],    // 1% chance
                    ['amount' => 250.00, 'weight' => 15],    // 1.5% chance
                    ['amount' => 100.00, 'weight' => 25],    // 2.5% chance
                    ['amount' => 50.00, 'weight' => 40],     // 4% chance
                    ['amount' => 25.00, 'weight' => 60],     // 6% chance
                    ['amount' => 0, 'weight' => 839],        // 83.9% chance (no prize)
                ],
                'total_cards' => 200,
                'is_active' => true,
            ],
            [
                'name' => 'Sorte Iniciante',
                'description' => 'Perfeita para quem está começando! Baixo risco, diversão garantida!',
                'price' => 1.00,
                'prize_structure' => [
                    ['amount' => 100.00, 'weight' => 1],     // 0.1% chance
                    ['amount' => 50.00, 'weight' => 3],      // 0.3% chance
                    ['amount' => 25.00, 'weight' => 6],      // 0.6% chance
                    ['amount' => 10.00, 'weight' => 15],     // 1.5% chance
                    ['amount' => 5.00, 'weight' => 25],      // 2.5% chance
                    ['amount' => 2.00, 'weight' => 50],      // 5% chance
                    ['amount' => 1.00, 'weight' => 100],     // 10% chance
                    ['amount' => 0, 'weight' => 800],        // 80% chance (no prize)
                ],
                'total_cards' => 5000,
                'is_active' => true,
            ],
            [
                'name' => 'Jackpot Supremo',
                'description' => 'O maior prêmio da plataforma! Até R$ 50.000 podem ser seus!',
                'price' => 50.00,
                'prize_structure' => [
                    ['amount' => 50000.00, 'weight' => 1],   // 0.1% chance
                    ['amount' => 25000.00, 'weight' => 1],   // 0.1% chance
                    ['amount' => 10000.00, 'weight' => 2],   // 0.2% chance
                    ['amount' => 5000.00, 'weight' => 3],    // 0.3% chance
                    ['amount' => 2500.00, 'weight' => 5],    // 0.5% chance
                    ['amount' => 1000.00, 'weight' => 8],    // 0.8% chance
                    ['amount' => 500.00, 'weight' => 15],    // 1.5% chance
                    ['amount' => 250.00, 'weight' => 20],    // 2% chance
                    ['amount' => 100.00, 'weight' => 30],    // 3% chance
                    ['amount' => 50.00, 'weight' => 50],     // 5% chance
                    ['amount' => 0, 'weight' => 865],        // 86.5% chance (no prize)
                ],
                'total_cards' => 100,
                'is_active' => true,
            ],
        ];

        foreach ($scratchCards as $cardData) {
            ScratchCard::create($cardData);
        }
    }
}
