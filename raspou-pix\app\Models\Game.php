<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'scratch_card_id',
        'game_data',
        'prize_amount',
        'status',
        'completed_at',
    ];

    protected $casts = [
        'game_data' => 'array',
        'prize_amount' => 'decimal:2',
        'completed_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scratchCard()
    {
        return $this->belongsTo(ScratchCard::class);
    }

    public function scopePlaying($query)
    {
        return $query->where('status', 'playing');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function getFormattedPrizeAttribute()
    {
        return 'R$ ' . number_format($this->prize_amount, 2, ',', '.');
    }
}
