<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Transaction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Create test user
        $user = User::create([
            'name' => 'Usuário Teste',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('123456'),
        ]);

        // Add initial balance
        Transaction::create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 500.00,
            'status' => 'completed',
            'payment_method' => 'pix',
            'description' => 'Depósito inicial de boas-vindas'
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Administrador',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('admin123'),
        ]);

        // Add admin balance
        Transaction::create([
            'user_id' => $admin->id,
            'type' => 'deposit',
            'amount' => 10000.00,
            'status' => 'completed',
            'payment_method' => 'pix',
            'description' => 'Saldo administrativo'
        ]);
    }
}
