<?php

namespace App\Http\Controllers;

use App\Models\Achievement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AchievementController extends Controller
{
    public function index()
    {
        return view('achievements.index');
    }

    public function getUserAchievements(Request $request)
    {
        $user = $request->user();
        
        // Get all achievements
        $allAchievements = Achievement::where('is_active', true)->get();
        
        // Get user's earned achievements
        $earnedAchievements = $user->achievements()->get();
        $earnedIds = $earnedAchievements->pluck('id')->toArray();
        
        // Prepare achievements data
        $achievements = $allAchievements->map(function ($achievement) use ($earnedIds, $earnedAchievements) {
            $isEarned = in_array($achievement->id, $earnedIds);
            $earnedAchievement = $earnedAchievements->firstWhere('id', $achievement->id);
            
            return [
                'id' => $achievement->id,
                'name' => $achievement->name,
                'description' => $achievement->description,
                'icon' => $achievement->icon,
                'type' => $achievement->type,
                'points' => $achievement->points,
                'is_earned' => $isEarned,
                'earned_at' => $isEarned ? $earnedAchievement->pivot->earned_at->diffForHumans() : null,
                'requirement_value' => $achievement->requirement_value,
                'requirement_amount' => $achievement->requirement_amount,
                'progress' => $this->calculateProgress($achievement, $user)
            ];
        });

        // Calculate summary
        $summary = [
            'earned' => $earnedAchievements->count(),
            'total' => $allAchievements->count(),
            'points' => $earnedAchievements->sum('points'),
            'completion_rate' => $allAchievements->count() > 0 
                ? round(($earnedAchievements->count() / $allAchievements->count()) * 100, 1)
                : 0
        ];

        return response()->json([
            'achievements' => $achievements,
            'summary' => $summary
        ]);
    }

    private function calculateProgress(Achievement $achievement, $user)
    {
        switch ($achievement->type) {
            case 'games_played':
                $current = $user->games()->count();
                $target = $achievement->requirement_value;
                break;
                
            case 'total_wins':
                $current = $user->games()->where('prize_amount', '>', 0)->count();
                $target = $achievement->requirement_value;
                break;
                
            case 'total_earned':
                $current = $user->transactions()->where('type', 'prize_win')->sum('amount');
                $target = $achievement->requirement_amount;
                break;
                
            case 'consecutive_wins':
                $current = $this->getConsecutiveWins($user);
                $target = $achievement->requirement_value;
                break;
                
            case 'big_win':
                $current = $user->transactions()->where('type', 'prize_win')->max('amount') ?? 0;
                $target = $achievement->requirement_amount;
                break;
                
            case 'daily_player':
                $current = $user->games()->whereDate('created_at', today())->count();
                $target = $achievement->requirement_value;
                break;
                
            default:
                return ['current' => 0, 'target' => 1, 'percentage' => 0];
        }

        $percentage = $target > 0 ? min(100, round(($current / $target) * 100, 1)) : 0;

        return [
            'current' => $current,
            'target' => $target,
            'percentage' => $percentage
        ];
    }

    private function getConsecutiveWins($user)
    {
        $games = $user->games()
            ->orderBy('completed_at', 'desc')
            ->get();

        $consecutiveWins = 0;
        $maxConsecutive = 0;

        foreach ($games as $game) {
            if ($game->prize_amount > 0) {
                $consecutiveWins++;
                $maxConsecutive = max($maxConsecutive, $consecutiveWins);
            } else {
                $consecutiveWins = 0;
            }
        }

        return $maxConsecutive;
    }

    public function checkUserAchievements(Request $request)
    {
        $user = $request->user();
        $newAchievements = $user->checkAchievements();

        return response()->json([
            'new_achievements' => $newAchievements->map(function($achievement) {
                return [
                    'id' => $achievement->id,
                    'name' => $achievement->name,
                    'description' => $achievement->description,
                    'icon' => $achievement->icon,
                    'points' => $achievement->points
                ];
            })
        ]);
    }
}
