<?php

namespace Database\Seeders;

use App\Models\Achievement;
use Illuminate\Database\Seeder;

class AchievementsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $achievements = [
            // Games Played Achievements
            [
                'name' => 'Primeiro Passo',
                'description' => 'Jogue sua primeira raspadinha',
                'icon' => '🎯',
                'type' => 'games_played',
                'requirement_value' => 1,
                'points' => 10
            ],
            [
                'name' => 'Jogador Iniciante',
                'description' => 'Jogue 10 raspadinhas',
                'icon' => '🎮',
                'type' => 'games_played',
                'requirement_value' => 10,
                'points' => 25
            ],
            [
                'name' => 'Jogador Experiente',
                'description' => 'Jogue 50 raspadinhas',
                'icon' => '🎪',
                'type' => 'games_played',
                'requirement_value' => 50,
                'points' => 50
            ],
            [
                'name' => 'Viciado em Raspadinhas',
                'description' => 'Jogue 100 raspadinhas',
                'icon' => '🎰',
                'type' => 'games_played',
                'requirement_value' => 100,
                'points' => 100
            ],

            // Total Wins Achievements
            [
                'name' => 'Primeira Vitória',
                'description' => 'Ganhe sua primeira raspadinha',
                'icon' => '🏆',
                'type' => 'total_wins',
                'requirement_value' => 1,
                'points' => 15
            ],
            [
                'name' => 'Sortudo',
                'description' => 'Ganhe 5 raspadinhas',
                'icon' => '🍀',
                'type' => 'total_wins',
                'requirement_value' => 5,
                'points' => 30
            ],
            [
                'name' => 'Ganhador Serial',
                'description' => 'Ganhe 25 raspadinhas',
                'icon' => '💎',
                'type' => 'total_wins',
                'requirement_value' => 25,
                'points' => 75
            ],

            // Total Earned Achievements
            [
                'name' => 'Primeiros Centavos',
                'description' => 'Ganhe seus primeiros R$ 10,00',
                'icon' => '💰',
                'type' => 'total_earned',
                'requirement_amount' => 10.00,
                'points' => 20
            ],
            [
                'name' => 'Colecionador de Moedas',
                'description' => 'Ganhe R$ 100,00 no total',
                'icon' => '🪙',
                'type' => 'total_earned',
                'requirement_amount' => 100.00,
                'points' => 50
            ],
            [
                'name' => 'Rico em Raspadinhas',
                'description' => 'Ganhe R$ 500,00 no total',
                'icon' => '💸',
                'type' => 'total_earned',
                'requirement_amount' => 500.00,
                'points' => 100
            ],
            [
                'name' => 'Milionário Virtual',
                'description' => 'Ganhe R$ 1.000,00 no total',
                'icon' => '👑',
                'type' => 'total_earned',
                'requirement_amount' => 1000.00,
                'points' => 200
            ],

            // Consecutive Wins Achievements
            [
                'name' => 'Sequência Dupla',
                'description' => 'Ganhe 2 raspadinhas seguidas',
                'icon' => '🔥',
                'type' => 'consecutive_wins',
                'requirement_value' => 2,
                'points' => 25
            ],
            [
                'name' => 'Hat Trick',
                'description' => 'Ganhe 3 raspadinhas seguidas',
                'icon' => '⚡',
                'type' => 'consecutive_wins',
                'requirement_value' => 3,
                'points' => 50
            ],
            [
                'name' => 'Sequência Imparável',
                'description' => 'Ganhe 5 raspadinhas seguidas',
                'icon' => '🌟',
                'type' => 'consecutive_wins',
                'requirement_value' => 5,
                'points' => 100
            ],

            // Big Win Achievements
            [
                'name' => 'Grande Prêmio',
                'description' => 'Ganhe R$ 50,00 em uma única raspadinha',
                'icon' => '💥',
                'type' => 'big_win',
                'requirement_amount' => 50.00,
                'points' => 75
            ],
            [
                'name' => 'Jackpot',
                'description' => 'Ganhe R$ 100,00 em uma única raspadinha',
                'icon' => '🎊',
                'type' => 'big_win',
                'requirement_amount' => 100.00,
                'points' => 150
            ],
            [
                'name' => 'Super Jackpot',
                'description' => 'Ganhe R$ 200,00 em uma única raspadinha',
                'icon' => '🌈',
                'type' => 'big_win',
                'requirement_amount' => 200.00,
                'points' => 300
            ],

            // Daily Player Achievements
            [
                'name' => 'Jogador Diário',
                'description' => 'Jogue 5 raspadinhas em um dia',
                'icon' => '📅',
                'type' => 'daily_player',
                'requirement_value' => 5,
                'points' => 30
            ],
            [
                'name' => 'Maratonista',
                'description' => 'Jogue 10 raspadinhas em um dia',
                'icon' => '🏃',
                'type' => 'daily_player',
                'requirement_value' => 10,
                'points' => 60
            ],

            // Special Achievements
            [
                'name' => 'Bem-vindo!',
                'description' => 'Cadastre-se na plataforma',
                'icon' => '🎉',
                'type' => 'special',
                'points' => 5
            ],
            [
                'name' => 'Explorador',
                'description' => 'Visite todas as seções da plataforma',
                'icon' => '🗺️',
                'type' => 'special',
                'points' => 20
            ]
        ];

        foreach ($achievements as $achievement) {
            Achievement::create($achievement);
        }
    }
}
