<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScratchCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'image',
        'prize_structure',
        'is_active',
        'total_cards',
        'cards_sold',
    ];

    protected $casts = [
        'prize_structure' => 'array',
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function games()
    {
        return $this->hasMany(Game::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function getAvailableCardsAttribute()
    {
        return $this->total_cards - $this->cards_sold;
    }

    public function getFormattedPriceAttribute()
    {
        return 'R$ ' . number_format($this->price, 2, ',', '.');
    }
}
