@extends('layouts.app')

@section('title', 'Ranking - Raspou Pix')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold mb-4">🏆 Ranking dos Campeões</h1>
        <p class="text-gray-300 text-lg">Veja quem são os maiores ganhadores da plataforma!</p>
    </div>

    <div class="grid lg:grid-cols-2 gap-8">
        <!-- Top Winners -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-6 text-center text-yellow-400">
                💰 <PERSON><PERSON> Ganhadores
            </h2>
            <div id="top-winners" class="space-y-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse">
                    @for($i = 1; $i <= 10; $i++)
                        <div class="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg mb-2">
                            <div class="w-8 h-8 bg-gray-600 rounded-full"></div>
                            <div class="flex-1">
                                <div class="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
                                <div class="h-3 bg-gray-600 rounded w-1/2"></div>
                            </div>
                            <div class="h-4 bg-gray-600 rounded w-20"></div>
                        </div>
                    @endfor
                </div>
            </div>
        </div>

        <!-- Top Players -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-6 text-center text-blue-400">
                🎮 Mais Ativos
            </h2>
            <div id="top-players" class="space-y-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse">
                    @for($i = 1; $i <= 10; $i++)
                        <div class="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg mb-2">
                            <div class="w-8 h-8 bg-gray-600 rounded-full"></div>
                            <div class="flex-1">
                                <div class="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
                                <div class="h-3 bg-gray-600 rounded w-1/2"></div>
                            </div>
                            <div class="h-4 bg-gray-600 rounded w-20"></div>
                        </div>
                    @endfor
                </div>
            </div>
        </div>
    </div>

    <!-- User Stats (if authenticated) -->
    @auth
    <div class="mt-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-center text-white">📊 Suas Estatísticas</h2>
        <div id="user-stats" class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <!-- Loading placeholders -->
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white animate-pulse">-</div>
                <div class="text-sm text-gray-200">Jogos</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white animate-pulse">-</div>
                <div class="text-sm text-gray-200">Vitórias</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white animate-pulse">R$ -</div>
                <div class="text-sm text-gray-200">Total Ganho</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white animate-pulse">-%</div>
                <div class="text-sm text-gray-200">Taxa de Vitória</div>
            </div>
        </div>
    </div>
    @endauth

    <!-- Refresh Button -->
    <div class="text-center mt-8">
        <button id="refresh-btn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
            <i class="fas fa-sync-alt mr-2"></i>Atualizar Rankings
        </button>
    </div>
</div>

@push('scripts')
<script>
class LeaderboardManager {
    constructor() {
        this.init();
    }

    async init() {
        await this.loadLeaderboard();
        @auth
        await this.loadUserStats();
        @endauth
        
        // Auto refresh every 30 seconds
        setInterval(() => this.loadLeaderboard(), 30000);
        
        // Manual refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadLeaderboard();
            @auth
            this.loadUserStats();
            @endauth
        });
    }

    async loadLeaderboard() {
        try {
            const response = await fetch('/api/stats/leaderboard');
            const data = await response.json();
            
            this.renderTopWinners(data.top_winners);
            this.renderTopPlayers(data.top_players);
        } catch (error) {
            console.error('Error loading leaderboard:', error);
        }
    }

    @auth
    async loadUserStats() {
        try {
            const response = await fetch('/api/stats/user');
            const data = await response.json();
            
            this.renderUserStats(data);
        } catch (error) {
            console.error('Error loading user stats:', error);
        }
    }
    @endauth

    renderTopWinners(winners) {
        const container = document.getElementById('top-winners');
        
        if (winners.length === 0) {
            container.innerHTML = '<p class="text-gray-400 text-center">Nenhum ganhador ainda...</p>';
            return;
        }

        container.innerHTML = winners.map(winner => `
            <div class="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white ${this.getPositionColor(winner.position)}">
                        ${winner.position}
                    </div>
                </div>
                <img src="${winner.avatar}" alt="${winner.name}" class="w-10 h-10 rounded-full">
                <div class="flex-1">
                    <h3 class="font-semibold text-white">${winner.name}</h3>
                    <p class="text-sm text-gray-400">Total ganho</p>
                </div>
                <div class="text-right">
                    <div class="font-bold text-yellow-400">R$ ${winner.total_won}</div>
                </div>
            </div>
        `).join('');
    }

    renderTopPlayers(players) {
        const container = document.getElementById('top-players');
        
        if (players.length === 0) {
            container.innerHTML = '<p class="text-gray-400 text-center">Nenhum jogador ainda...</p>';
            return;
        }

        container.innerHTML = players.map(player => `
            <div class="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white ${this.getPositionColor(player.position)}">
                        ${player.position}
                    </div>
                </div>
                <img src="${player.avatar}" alt="${player.name}" class="w-10 h-10 rounded-full">
                <div class="flex-1">
                    <h3 class="font-semibold text-white">${player.name}</h3>
                    <p class="text-sm text-gray-400">Jogos jogados</p>
                </div>
                <div class="text-right">
                    <div class="font-bold text-blue-400">${player.total_games}</div>
                </div>
            </div>
        `).join('');
    }

    @auth
    renderUserStats(stats) {
        const container = document.getElementById('user-stats');
        
        container.innerHTML = `
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">${stats.total_games}</div>
                <div class="text-sm text-gray-200">Jogos</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">${stats.total_wins}</div>
                <div class="text-sm text-gray-200">Vitórias</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">R$ ${parseFloat(stats.total_won).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
                <div class="text-sm text-gray-200">Total Ganho</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">${stats.win_rate}%</div>
                <div class="text-sm text-gray-200">Taxa de Vitória</div>
            </div>
        `;
    }
    @endauth

    getPositionColor(position) {
        switch(position) {
            case 1: return 'bg-yellow-500'; // Gold
            case 2: return 'bg-gray-400';   // Silver
            case 3: return 'bg-yellow-600'; // Bronze
            default: return 'bg-blue-600';  // Default
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new LeaderboardManager();
});
</script>
@endpush
@endsection
