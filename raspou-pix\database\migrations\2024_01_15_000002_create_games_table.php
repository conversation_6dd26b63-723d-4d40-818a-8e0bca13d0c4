<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('games', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('scratch_card_id')->constrained()->onDelete('cascade');
            $table->json('game_data'); // Stores the scratch positions and symbols
            $table->decimal('prize_amount', 10, 2)->default(0);
            $table->enum('status', ['playing', 'completed', 'expired'])->default('playing');
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['scratch_card_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('games');
    }
};
