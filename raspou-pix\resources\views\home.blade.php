@extends('layouts.app')

@section('title', 'Ra<PERSON><PERSON> Pix - Raspadinhas Online')

@section('content')
<div class="space-y-12">
    <!-- Hero Section -->
    <section class="text-center py-16">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-red-400">
                🎰 Bem-vindo ao Raspou Pix!
            </h1>
            <p class="text-xl text-gray-300 mb-8">
                A maior plataforma de raspadinhas online do Brasil! Jogue, ganhe e receba na hora via PIX.
            </p>
            <div class="flex justify-center space-x-4">
                @auth
                    <a href="{{ route('scratch-cards.index') }}" class="btn-primary px-8 py-4 rounded-lg text-white font-semibold text-lg">
                        <i class="fas fa-play mr-2"></i><PERSON><PERSON>
                    </a>
                @else
                    <a href="{{ route('register') }}" class="btn-primary px-8 py-4 rounded-lg text-white font-semibold text-lg">
                        <i class="fas fa-user-plus mr-2"></i>Cadastre-se Grátis
                    </a>
                    <a href="{{ route('login') }}" class="border border-purple-400 text-purple-400 px-8 py-4 rounded-lg hover:bg-purple-400 hover:text-white transition-colors text-lg">
                        <i class="fas fa-sign-in-alt mr-2"></i>Entrar
                    </a>
                @endauth
            </div>
        </div>
    </section>

    <!-- Live Statistics Widget -->
    @include('components.live-stats')

    <!-- Features Section -->
    <section class="py-16">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12">Por que escolher o Raspou Pix?</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-gray-800 p-8 rounded-lg text-center">
                    <div class="text-4xl mb-4">⚡</div>
                    <h3 class="text-xl font-semibold mb-4">Pagamento Instantâneo</h3>
                    <p class="text-gray-300">Receba seus prêmios na hora via PIX. Sem complicações, sem demora!</p>
                </div>
                <div class="bg-gray-800 p-8 rounded-lg text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold mb-4">Chances Reais</h3>
                    <p class="text-gray-300">Sistema transparente com chances reais de ganhar. Tudo auditado e confiável!</p>
                </div>
                <div class="bg-gray-800 p-8 rounded-lg text-center">
                    <div class="text-4xl mb-4">🔒</div>
                    <h3 class="text-xl font-semibold mb-4">100% Seguro</h3>
                    <p class="text-gray-300">Plataforma segura com criptografia avançada. Seus dados estão protegidos!</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Scratch Cards Preview -->
    @if($scratchCards->count() > 0)
    <section class="py-16">
        <div class="max-w-6xl mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl font-bold">🎫 Raspadinhas Disponíveis</h2>
                <a href="{{ route('scratch-cards.index') }}" class="text-purple-400 hover:text-purple-300 transition-colors">
                    Ver todas <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($scratchCards as $card)
                <div class="scratch-card p-6 text-center">
                    <div class="text-4xl mb-4">🎰</div>
                    <h3 class="text-xl font-semibold mb-2">{{ $card->name }}</h3>
                    <p class="text-gray-200 text-sm mb-4">{{ Str::limit($card->description, 60) }}</p>
                    <div class="text-2xl font-bold text-yellow-400 mb-4">{{ $card->formatted_price }}</div>
                    <div class="text-sm text-gray-300 mb-4">
                        <i class="fas fa-ticket-alt mr-1"></i>{{ $card->available_cards }} disponíveis
                    </div>
                    @auth
                        <button onclick="purchaseCard({{ $card->id }})" class="w-full bg-yellow-500 hover:bg-yellow-400 text-gray-900 font-semibold py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-shopping-cart mr-2"></i>Comprar
                        </button>
                    @else
                        <a href="{{ route('login') }}" class="block w-full bg-yellow-500 hover:bg-yellow-400 text-gray-900 font-semibold py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-sign-in-alt mr-2"></i>Entrar para Jogar
                        </a>
                    @endauth
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Últimos Ganhadores Section -->
    @if($recentWinners->count() > 0)
    <section class="mb-12">
        <h2 class="text-2xl font-bold text-white mb-6">🏆 Últimos Ganhadores</h2>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            @foreach($recentWinners as $winner)
            <div class="card p-4 md:p-5 hover:shadow-lg transition-shadow">
                <!-- Layout principal com grid -->
                <div class="grid grid-cols-3 gap-3">
                    <!-- Imagem do jogo (1/3 do espaço) -->
                    <div class="col-span-1">
                        <div class="relative rounded-lg overflow-hidden h-full flex items-center justify-center" style="min-height: 80px; max-height: 120px; background-color: #1a2234;">
                            <img src="{{ $winner->scratchCard->image_url ?? 'https://files.raspoupix.com/premio-dinheiro/100_reais.png' }}"
                                 alt="{{ $winner->scratchCard->name }}"
                                 class="h-full w-full object-contain"
                                 onerror="this.src='https://files.raspoupix.com/premio-dinheiro/100_reais.png'; this.onerror=null;">
                        </div>
                    </div>

                    <!-- Informações do ganhador (2/3 do espaço) -->
                    <div class="col-span-2">
                        <!-- Badge do tipo de prêmio -->
                        <div class="flex justify-end mb-1">
                            <span class="badge bg-yellow-600 text-white text-xs px-2 py-1 rounded-full">
                                <i class="fas fa-crown mr-1"></i> Raspadinha
                            </span>
                        </div>

                        <!-- Dados do usuário -->
                        <p class="text-xs text-gray-400 mb-1">Ganhador</p>
                        <p class="font-bold text-white text-sm mb-1">{{ substr($winner->user->name, 0, strpos($winner->user->name, ' ') ?: strlen($winner->user->name)) }} {{ substr($winner->user->name, strpos($winner->user->name, ' ') + 1, 1) }}.</p>
                        <p class="text-xs text-gray-400 mb-3"></p>

                        <!-- Informação do prêmio -->
                        <div>
                            <p class="text-xs text-gray-400 mb-1">Prêmio</p>
                            <div>
                                <p class="text-lg font-bold text-green-500">
                                    R$ {{ number_format($winner->prize_amount, 2, ',', '.') }}
                                </p>
                            </div>
                        </div>

                        <!-- Data do prêmio -->
                        <p class="text-xs text-gray-400 mt-2">
                            <i class="far fa-calendar-alt mr-1"></i> {{ $winner->created_at->format('d/m/Y') }}
                        </p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Botão para ver mais ganhadores -->
        <div class="text-center mt-6">
            <a href="{{ route('winners') }}" class="btn-secondary inline-block">
                <i class="fas fa-trophy mr-2"></i> Ver Todos os Ganhadores
            </a>
        </div>
    </section>
    @endif

    <!-- How to Play Section -->
    <section class="py-16 bg-gray-800 rounded-lg">
        <div class="max-w-4xl mx-auto px-8">
            <h2 class="text-3xl font-bold text-center mb-12">🎮 Como Jogar</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">1</div>
                    <h3 class="text-xl font-semibold mb-2">Escolha sua Raspadinha</h3>
                    <p class="text-gray-300">Selecione entre várias opções com diferentes prêmios e valores.</p>
                </div>
                <div class="text-center">
                    <div class="bg-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">2</div>
                    <h3 class="text-xl font-semibold mb-2">Raspe e Descubra</h3>
                    <p class="text-gray-300">Clique nas posições para revelar os símbolos escondidos.</p>
                </div>
                <div class="text-center">
                    <div class="bg-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">3</div>
                    <h3 class="text-xl font-semibold mb-2">Ganhe e Receba</h3>
                    <p class="text-gray-300">Se ganhar, receba seu prêmio instantaneamente via PIX!</p>
                </div>
            </div>
        </div>
    </section>
</div>

@auth
@push('scripts')
<script>
function purchaseCard(cardId) {
    if (confirm('Deseja comprar esta raspadinha?')) {
        $.post(`/games/purchase/${cardId}`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
        .done(function(response) {
            if (response.success) {
                window.location.href = response.redirect;
            } else {
                alert(response.message);
            }
        })
        .fail(function() {
            alert('Erro ao processar compra. Tente novamente.');
        });
    }
}
</script>
@endpush
@endauth
@endsection
