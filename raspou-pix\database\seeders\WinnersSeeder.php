<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Game;
use App\Models\ScratchCard;
use App\Models\Transaction;
use Illuminate\Support\Facades\Hash;

class WinnersSeeder extends Seeder
{
    public function run()
    {
        // Criar alguns usuários fictícios para os ganhadores
        $winners = [
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON> Costa', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON><PERSON><PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON> Souza', 'email' => '<EMAIL>'],
        ];

        $createdUsers = [];
        foreach ($winners as $winner) {
            $user = User::firstOrCreate(
                ['email' => $winner['email']],
                [
                    'name' => $winner['name'],
                    'password' => Hash::make('123456'),
                    'balance' => rand(50, 500),
                ]
            );
            $createdUsers[] = $user;
        }

        // Pegar as raspadinhas existentes
        $scratchCards = ScratchCard::all();

        if ($scratchCards->isEmpty()) {
            $this->command->info('Nenhuma raspadinha encontrada. Execute o ScratchCardSeeder primeiro.');
            return;
        }

        // Criar jogos com prêmios para simular ganhadores
        $prizes = [100, 50, 25, 10, 5, 200, 75, 150, 30, 20, 500, 250, 80, 40, 15];

        foreach ($createdUsers as $index => $user) {
            // Criar alguns jogos perdedores primeiro
            for ($i = 0; $i < rand(2, 5); $i++) {
                Game::create([
                    'user_id' => $user->id,
                    'scratch_card_id' => $scratchCards->random()->id,
                    'status' => 'completed',
                    'prize_amount' => 0,
                    'game_data' => json_encode([
                        'positions' => [1, 2, 3, 4, 5, 6, 7, 8, 9],
                        'symbols' => ['🍎', '🍌', '🍒', '🍎', '🍌', '🍒', '🍎', '🍌', '🍒']
                    ]),
                    'completed_at' => now()->subDays(rand(1, 30))->subHours(rand(0, 23)),
                    'created_at' => now()->subDays(rand(1, 30))->subHours(rand(0, 23)),
                ]);
            }

            // Criar um jogo ganhador
            $prizeAmount = $prizes[$index] ?? rand(10, 100);
            $scratchCard = $scratchCards->random();

            $game = Game::create([
                'user_id' => $user->id,
                'scratch_card_id' => $scratchCard->id,
                'status' => 'completed',
                'prize_amount' => $prizeAmount,
                'game_data' => json_encode([
                    'positions' => [1, 2, 3, 4, 5, 6, 7, 8, 9],
                    'symbols' => ['💰', '💰', '💰', '💰', '💰', '💰', '💰', '💰', '💰']
                ]),
                'completed_at' => now()->subDays(rand(0, 15))->subHours(rand(0, 23)),
                'created_at' => now()->subDays(rand(0, 15))->subHours(rand(0, 23)),
            ]);

            // Criar transação de prêmio
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'prize_win',
                'amount' => $prizeAmount,
                'status' => 'completed',
                'description' => "Prêmio da raspadinha: {$scratchCard->name}",
                'created_at' => $game->created_at,
            ]);

            // Atualizar saldo do usuário
            $user->increment('balance', $prizeAmount);

            // Criar algumas transações de compra
            for ($i = 0; $i < rand(3, 8); $i++) {
                $purchaseCard = $scratchCards->random();
                Transaction::create([
                    'user_id' => $user->id,
                    'type' => 'game_purchase',
                    'amount' => $purchaseCard->price,
                    'status' => 'completed',
                    'description' => "Compra da raspadinha: {$purchaseCard->name}",
                    'created_at' => now()->subDays(rand(1, 30))->subHours(rand(0, 23)),
                ]);
            }
        }

        $this->command->info('Ganhadores fictícios criados com sucesso!');
    }
}
