# 🎰 Raspou Pix - Plataforma de Raspadinhas Online

Uma plataforma completa de jogos de raspadinhas online desenvolvida em Laravel, com integração PIX, sistema de conquistas, rankings em tempo real e muito mais!

## ✨ Funcionalidades Principais

### 🎮 Sistema de Jogos
- **Raspadinhas Interativas**: Interface moderna com efeitos visuais
- **Múltiplos Tipos de Jogos**: Diferentes categorias de raspadinhas
- **Sistema de Probabilidades**: Distribuição justa de prêmios
- **Animações e Efeitos**: Confetes, celebrações e transições suaves

### 💰 Sistema Financeiro
- **Integração PIX**: Pagamentos instantâneos brasileiros
- **Carteira Digital**: Gerenciamento de saldo em tempo real
- **Histórico de Transações**: Acompanhamento completo
- **Sistema de Bônus**: Bônus de boas-vindas automático

### 🏆 Sistema de Conquistas
- **20+ Conquistas**: Diferentes categorias e níveis
- **Sistema de Pontos**: Gamificação completa
- **Notificações em Tempo Real**: Alertas de novas conquistas
- **Progresso Visual**: Acompanhamento do progresso

### 📊 Estatísticas e Rankings
- **Estatísticas em Tempo Real**: Dados atualizados automaticamente
- **Ranking de Ganhadores**: Top 10 maiores ganhadores
- **Ranking de Jogadores**: Top 10 mais ativos
- **Estatísticas Pessoais**: Dados individuais detalhados

### 🔐 Sistema de Autenticação
- **Registro e Login**: Sistema completo de usuários
- **Validação CPF**: Validação brasileira
- **Sessões Seguras**: Gerenciamento de sessões
- **Perfil de Usuário**: Área pessoal completa

### 📱 Interface Responsiva
- **Design Mobile-First**: Otimizado para dispositivos móveis
- **Navegação Inferior**: Menu fixo para fácil acesso
- **Tema Escuro**: Interface moderna e elegante
- **Notificações Toast**: Sistema de alertas não-intrusivo

## 🛠️ Tecnologias Utilizadas

### Backend
- **Laravel 10.x**: Framework PHP moderno
- **MySQL**: Banco de dados relacional
- **Eloquent ORM**: Mapeamento objeto-relacional
- **Laravel Authentication**: Sistema de autenticação nativo

### Frontend
- **Blade Templates**: Sistema de templates do Laravel
- **Tailwind CSS**: Framework CSS utilitário
- **JavaScript Vanilla**: Interatividade sem dependências
- **AJAX**: Comunicação assíncrona

### Recursos Especiais
- **Localização Brasileira**: CPF, telefone, moeda
- **Sistema de Seeders**: Dados de exemplo
- **Migrações**: Versionamento do banco de dados
- **API RESTful**: Endpoints para dados em tempo real

## 🎯 Funcionalidades Implementadas

### ✅ Sistema de Jogos Completo
- Interface de raspadinha com layout profissional
- Sistema de compra e débito automático
- Revelação de posições com animações
- Cálculo de prêmios baseado em probabilidades
- Atualização de saldo em tempo real

### ✅ Sistema de Conquistas Gamificado
- 20+ conquistas em 7 categorias diferentes
- Sistema de pontos e progresso
- Notificações especiais para novas conquistas
- Interface dedicada para visualizar conquistas

### ✅ Estatísticas em Tempo Real
- Widget de estatísticas na home page
- Dados atualizados automaticamente a cada 10 segundos
- Ticker de ganhadores recentes com animação
- Rankings de maiores ganhadores e jogadores mais ativos

### ✅ Sistema de Notificações Avançado
- Notificações toast elegantes
- Diferentes tipos: sucesso, erro, prêmio, conquista
- Animações de entrada e saída
- Auto-dismiss configurável

### ✅ Interface Responsiva Completa
- Design mobile-first otimizado
- Menu de navegação inferior fixo
- Tema escuro moderno
- Efeitos visuais e animações CSS

## 🚀 Como Usar

1. **Acesse a plataforma** em seu navegador
2. **Registre-se** com seus dados (nome, email, CPF)
3. **Receba R$ 50,00** de bônus de boas-vindas
4. **Escolha uma raspadinha** na seção "Raspadinhas"
5. **Jogue e ganhe** prêmios instantâneos
6. **Desbloqueie conquistas** conforme joga
7. **Acompanhe seu progresso** no ranking

## 📊 Páginas Disponíveis

- **🏠 Home**: Estatísticas em tempo real e apresentação
- **🎰 Raspadinhas**: Catálogo de jogos disponíveis
- **🎮 Jogo**: Interface de jogo interativa
- **🏆 Ranking**: Rankings de ganhadores e jogadores
- **🎖️ Conquistas**: Sistema de conquistas pessoais
- **👥 Vencedores**: Lista de ganhadores recentes
- **💰 Carteira**: Histórico financeiro e transações
- **👤 Perfil**: Área pessoal do usuário

## 🎨 Recursos Visuais Especiais

### Efeitos de Celebração
- **Confetes Animados**: 50 partículas coloridas caindo
- **Notificações de Prêmio**: Design especial dourado
- **Modal de Conquistas**: Popup exclusivo com gradiente
- **Animações CSS**: Transições suaves em toda interface

### Sistema de Cores
- **Tema Escuro**: Fundo escuro elegante
- **Gradientes**: Efeitos visuais modernos
- **Cores Semânticas**: Verde (sucesso), vermelho (erro), etc.
- **Hover Effects**: Interatividade visual

## 🔧 Arquitetura Técnica

### Modelos de Dados
- **User**: Usuários com saldo e conquistas
- **ScratchCard**: Tipos de raspadinhas
- **Game**: Jogos individuais com estado
- **Transaction**: Histórico financeiro
- **Achievement**: Sistema de conquistas
- **UserAchievement**: Conquistas dos usuários

### Controllers
- **GameController**: Lógica de jogos
- **StatsController**: Estatísticas em tempo real
- **AchievementController**: Sistema de conquistas
- **AuthControllers**: Autenticação segura

### APIs RESTful
- `GET /api/stats/realtime` - Estatísticas em tempo real
- `GET /api/stats/user` - Dados do usuário
- `GET /api/stats/leaderboard` - Rankings
- `GET /api/achievements` - Conquistas do usuário
- `POST /games/{game}/scratch` - Jogar raspadinha

## 🎉 Projeto Completo e Funcional!

O **Raspou Pix** está 100% funcional com todas as funcionalidades implementadas:

✅ **Sistema de Jogos** - Raspadinhas interativas
✅ **Sistema Financeiro** - Carteira digital com PIX
✅ **Sistema de Conquistas** - 20+ conquistas gamificadas
✅ **Estatísticas em Tempo Real** - Dados atualizados automaticamente
✅ **Rankings** - Leaderboards de ganhadores e jogadores
✅ **Interface Responsiva** - Design mobile-first
✅ **Notificações** - Sistema de alertas elegante
✅ **Autenticação** - Login/registro seguro
✅ **Efeitos Visuais** - Animações e celebrações

**🚀 Pronto para uso em produção!**

---

**Desenvolvido com ❤️ para a comunidade brasileira de jogos online!**

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
