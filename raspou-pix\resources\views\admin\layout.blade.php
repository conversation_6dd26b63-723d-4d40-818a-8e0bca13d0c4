<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Admin') - RaspouPix</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Roboto Condensed', sans-serif;
        }

        :root {
            --ci-primary-color: #FFF0BB;
            --ci-primary-opacity-color: #062064;
            --ci-secundary-color: #FFF0BB;
            --ci-gray-dark: #1542b3;
            --ci-gray-light: #062064;
            --ci-gray-medium: #6FA4EF;
            --ci-gray-over: #05309F;
            --title-color: #fff;
            --text-color: #fff;
            --background-color: #24262B;
            --standard-color: #1C1E22;
            --shadow-color: #111415;
            --yellow-color: #FFBF39;
            --yellow-dark-color: #d7a026;
            --border-radius: 5px;
        }

        .sidebar-active {
            background: linear-gradient(135deg, var(--ci-gray-dark), var(--ci-gray-medium)) !important;
            color: var(--title-color) !important;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .admin-sidebar {
            background: var(--ci-gray-light) !important;
            color: var(--title-color);
        }

        .admin-header {
            background: linear-gradient(135deg, var(--ci-primary-color), #fff) !important;
            color: var(--ci-gray-dark);
        }

        .admin-card {
            background: white;
            border: 1px solid var(--ci-gray-medium);
            border-radius: var(--border-radius);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--ci-gray-dark), var(--ci-gray-medium));
            color: var(--title-color);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--ci-gray-over), var(--ci-gray-dark));
        }

        .text-primary-custom {
            color: var(--ci-gray-dark) !important;
        }

        .bg-primary-custom {
            background: var(--ci-gray-dark) !important;
        }

        .border-primary-custom {
            border-color: var(--ci-gray-medium) !important;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 admin-sidebar text-white">
            <div class="p-4">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background: var(--yellow-color);">
                        <i class="fas fa-coins" style="color: var(--ci-gray-dark);"></i>
                    </div>
                    <h1 class="text-xl font-bold" style="color: var(--title-color);">RaspouPix</h1>
                </div>
                <p class="text-gray-400 text-sm mt-1">Painel Administrativo</p>
            </div>

            <nav class="mt-8">
                <a href="{{ route('admin.dashboard') }}" class="flex items-center px-4 py-3 text-gray-300 hover:opacity-80 {{ request()->routeIs('admin.dashboard') ? 'sidebar-active text-white' : '' }}" style="color: var(--title-color);">
                    <i class="fas fa-tachometer-alt mr-3" style="color: var(--yellow-color);"></i>
                    Dashboard
                </a>
                <a href="{{ route('admin.users') }}" class="flex items-center px-4 py-3 text-gray-300 hover:opacity-80 {{ request()->routeIs('admin.users') ? 'sidebar-active text-white' : '' }}" style="color: var(--title-color);">
                    <i class="fas fa-users mr-3" style="color: var(--yellow-color);"></i>
                    Usuários
                </a>
                <a href="{{ route('admin.games') }}" class="flex items-center px-4 py-3 text-gray-300 hover:opacity-80 {{ request()->routeIs('admin.games') ? 'sidebar-active text-white' : '' }}" style="color: var(--title-color);">
                    <i class="fas fa-gamepad mr-3" style="color: var(--yellow-color);"></i>
                    Jogos
                </a>
                <a href="{{ route('admin.reports') }}" class="flex items-center px-4 py-3 text-gray-300 hover:opacity-80 {{ request()->routeIs('admin.reports') ? 'sidebar-active text-white' : '' }}" style="color: var(--title-color);">
                    <i class="fas fa-chart-bar mr-3" style="color: var(--yellow-color);"></i>
                    Relatórios
                </a>
                <a href="{{ route('admin.settings') }}" class="flex items-center px-4 py-3 text-gray-300 hover:opacity-80 {{ request()->routeIs('admin.settings') ? 'sidebar-active text-white' : '' }}" style="color: var(--title-color);">
                    <i class="fas fa-cog mr-3" style="color: var(--yellow-color);"></i>
                    Configurações
                </a>

                <div class="border-t mt-8 pt-4" style="border-color: var(--ci-gray-medium);">
                    <a href="{{ route('home') }}" class="flex items-center px-4 py-3 hover:opacity-80" style="color: var(--title-color);">
                        <i class="fas fa-home mr-3" style="color: var(--yellow-color);"></i>
                        Voltar ao Site
                    </a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="flex items-center w-full px-4 py-3 hover:opacity-80" style="color: var(--title-color);">
                            <i class="fas fa-sign-out-alt mr-3" style="color: var(--yellow-color);"></i>
                            Sair
                        </button>
                    </form>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Header -->
            <header class="admin-header shadow-sm border-b">
                <div class="flex items-center justify-between px-6 py-4">
                    <div>
                        <h2 class="text-2xl font-bold text-primary-custom">@yield('page-title', 'Dashboard')</h2>
                        <p class="text-sm" style="color: var(--ci-gray-dark);">@yield('page-description', 'Visão geral do sistema de raspadinhas')</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm" style="color: var(--ci-gray-dark);">Olá, {{ auth()->user()->name }}</p>
                            <p class="text-xs" style="color: var(--ci-gray-medium);">{{ now()->format('d/m/Y H:i') }}</p>
                        </div>
                        <div class="w-10 h-10 rounded-full flex items-center justify-center" style="background: var(--ci-gray-dark);">
                            <i class="fas fa-user" style="color: var(--title-color);"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <main class="flex-1 overflow-y-auto p-6">
                @if(session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        {{ session('success') }}
                    </div>
                @endif

                @if(session('error'))
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        {{ session('error') }}
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-green-100, .bg-red-100');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html>
