<!-- Live Statistics Widget -->
<div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 mb-8">
    <h2 class="text-2xl font-bold text-white mb-6 text-center">📊 Estatísticas em Tempo Real</h2>
    
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <!-- Total Players -->
        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
            <div class="text-3xl font-bold text-white" id="total-players">-</div>
            <div class="text-sm text-gray-200">Jogadores</div>
        </div>
        
        <!-- Games Today -->
        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
            <div class="text-3xl font-bold text-white" id="games-today">-</div>
            <div class="text-sm text-gray-200">Jogos Hoje</div>
        </div>
        
        <!-- Prizes Today -->
        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
            <div class="text-3xl font-bold text-yellow-300" id="prizes-today">R$ -</div>
            <div class="text-sm text-gray-200">Prêmios Hoje</div>
        </div>
        
        <!-- Biggest Win -->
        <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
            <div class="text-3xl font-bold text-green-300" id="biggest-win">R$ -</div>
            <div class="text-sm text-gray-200">Maior Prêmio</div>
        </div>
    </div>
    
    <!-- Recent Winners Ticker -->
    <div class="bg-black bg-opacity-30 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-white mb-3">🏆 Ganhadores Recentes</h3>
        <div class="overflow-hidden">
            <div id="winners-ticker" class="flex space-x-8 animate-scroll">
                <!-- Winners will be populated here -->
            </div>
        </div>
    </div>
</div>

<style>
@keyframes scroll {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.animate-scroll {
    animation: scroll 30s linear infinite;
}

.animate-scroll:hover {
    animation-play-state: paused;
}
</style>

<script>
class LiveStats {
    constructor() {
        this.updateInterval = 10000; // 10 seconds
        this.init();
    }

    init() {
        this.updateStats();
        setInterval(() => this.updateStats(), this.updateInterval);
    }

    async updateStats() {
        try {
            const response = await fetch('/api/stats/realtime');
            const data = await response.json();
            
            this.updateElements(data);
        } catch (error) {
            console.error('Error fetching stats:', error);
        }
    }

    updateElements(data) {
        // Update counters with animation
        this.animateCounter('total-players', data.total_players);
        this.animateCounter('games-today', data.games_played_today);
        
        // Update monetary values
        document.getElementById('prizes-today').textContent = 
            'R$ ' + parseFloat(data.total_prizes_today || 0).toLocaleString('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            
        document.getElementById('biggest-win').textContent = 
            'R$ ' + parseFloat(data.biggest_win_today || 0).toLocaleString('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });

        // Update winners ticker
        this.updateWinnersTicker(data.recent_winners || []);
    }

    animateCounter(elementId, targetValue) {
        const element = document.getElementById(elementId);
        const currentValue = parseInt(element.textContent) || 0;
        const increment = Math.ceil((targetValue - currentValue) / 20);
        
        if (currentValue !== targetValue) {
            const timer = setInterval(() => {
                const current = parseInt(element.textContent) || 0;
                if (current < targetValue) {
                    element.textContent = Math.min(current + increment, targetValue);
                } else {
                    element.textContent = targetValue;
                    clearInterval(timer);
                }
            }, 50);
        }
    }

    updateWinnersTicker(winners) {
        const ticker = document.getElementById('winners-ticker');
        
        if (winners.length === 0) {
            ticker.innerHTML = '<div class="text-gray-300 whitespace-nowrap">Nenhum ganhador recente...</div>';
            return;
        }

        ticker.innerHTML = winners.map(winner => `
            <div class="flex items-center space-x-2 whitespace-nowrap">
                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-black font-bold text-sm">
                    ${winner.player_name.charAt(0).toUpperCase()}
                </div>
                <div class="text-white">
                    <span class="font-semibold">${winner.player_name}</span>
                    <span class="text-yellow-300">ganhou R$ ${winner.prize}</span>
                    <span class="text-gray-300">em ${winner.game}</span>
                    <span class="text-gray-400 text-sm">(${winner.time})</span>
                </div>
            </div>
        `).join('');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on a page that should show stats
    if (document.getElementById('total-players')) {
        new LiveStats();
    }
});
</script>
