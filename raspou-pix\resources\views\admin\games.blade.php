@extends('admin.layout')

@section('title', 'Jogos')
@section('page-title', 'Jogos')
@section('page-description', 'Histórico de jogos e raspadinhas')

@section('content')
<div class="bg-white rounded-lg shadow-md">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">Histórico de Jogos</h3>
            <div class="flex items-center space-x-2">
                <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Todos os status</option>
                    <option value="won">Ganhou</option>
                    <option value="lost">Perdeu</option>
                </select>
                <input type="date" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i class="fas fa-filter"></i> Filtrar
                </button>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Usuário
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Custo
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Prêmio
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Resultado
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Símbolos
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Data
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ações
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($games as $game)
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">#{{ $game->id }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs font-semibold">{{ strtoupper(substr($game->user->name ?? 'U', 0, 1)) }}</span>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{{ $game->user->name ?? 'Usuário Deletado' }}</div>
                                <div class="text-sm text-gray-500">{{ $game->user->email ?? 'N/A' }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">R$ {{ number_format($game->cost, 2, ',', '.') }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium {{ $game->prize_amount > 0 ? 'text-green-600' : 'text-gray-400' }}">
                            R$ {{ number_format($game->prize_amount, 2, ',', '.') }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @if($game->prize_amount > 0)
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                <i class="fas fa-trophy mr-1"></i> Ganhou
                            </span>
                        @else
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                <i class="fas fa-times mr-1"></i> Perdeu
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center space-x-1">
                            @if($game->symbols)
                                @foreach(json_decode($game->symbols) as $symbol)
                                    <span class="inline-flex items-center justify-center w-6 h-6 text-xs bg-gray-100 rounded border">
                                        {{ $symbol }}
                                    </span>
                                @endforeach
                            @else
                                <span class="text-gray-400 text-xs">N/A</span>
                            @endif
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ $game->created_at->format('d/m/Y') }}</div>
                        <div class="text-sm text-gray-500">{{ $game->created_at->format('H:i:s') }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <button class="text-blue-600 hover:text-blue-900" title="Ver detalhes" onclick="showGameDetails({{ $game->id }})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <form method="POST" action="{{ route('admin.games.delete', $game->id) }}" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-900" title="Deletar" onclick="return confirm('Tem certeza que deseja deletar este jogo?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                        Nenhum jogo encontrado
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    @if($games->hasPages())
    <div class="px-6 py-4 border-t border-gray-200">
        {{ $games->links() }}
    </div>
    @endif
</div>

<!-- Game Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-gamepad text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total de Jogos</p>
                <p class="text-2xl font-bold text-blue-600">{{ $games->total() }}</p>
                <p class="text-xs text-gray-500">Todos os tempos</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-trophy text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Jogos Vencedores</p>
                <p class="text-2xl font-bold text-green-600">{{ $games->where('prize_amount', '>', 0)->count() }}</p>
                <p class="text-xs text-gray-500">{{ $games->count() > 0 ? number_format(($games->where('prize_amount', '>', 0)->count() / $games->count()) * 100, 1) : 0 }}% de vitórias</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <i class="fas fa-coins text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Receita Total</p>
                <p class="text-2xl font-bold text-yellow-600">R$ {{ number_format($games->sum('cost'), 2, ',', '.') }}</p>
                <p class="text-xs text-gray-500">Em jogos</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 text-red-600">
                <i class="fas fa-gift text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Prêmios Pagos</p>
                <p class="text-2xl font-bold text-red-600">R$ {{ number_format($games->sum('prize_amount'), 2, ',', '.') }}</p>
                <p class="text-xs text-gray-500">Em prêmios</p>
            </div>
        </div>
    </div>
</div>

<script>
function showGameDetails(gameId) {
    // Implementar modal com detalhes do jogo
    alert('Detalhes do jogo #' + gameId + ' - Funcionalidade a ser implementada');
}
</script>
@endsection
