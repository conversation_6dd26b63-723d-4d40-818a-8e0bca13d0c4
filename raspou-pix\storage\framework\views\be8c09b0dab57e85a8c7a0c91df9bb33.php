<?php $__env->startSection('title', 'Raspadinha - ' . $game->scratchCard->name); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-b from-green-400 to-green-600 p-4">
    <!-- Main Container seguindo o layout fornecido -->
    <div class="md:flex md:justify-between md:max-w-[900px] mx-auto">
        <!-- Game Area -->
        <div class="flex flex-col w-full justify-center">
            <!-- Scratch Card Container -->
            <div class="w-full max-w-[340px] md:max-w-[580px] min-h-[350px] md:min-h-[530px] mx-auto relative rounded-md overflow-hidden bg-center bg-cover"
                 style="background-image: url('https://worldgamesbr.com.br/wp-content/uploads/2025/07/RASPE-AQUI-1.png'); height: 350px;">

                <!-- Background overlay com botão inicial -->
                <div class="w-full h-[350px] md:min-h-[530px] bg-gradient-to-br from-gray-600 to-gray-800 bg-cover bg-center flex items-center justify-center relative"
                     id="initial-overlay">
                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20"></div>
                    <button class="bg-red-600 hover:bg-red-700 text-white font-bold px-6 py-2 rounded shadow-md w-[280px] h-[52px] text-lg z-40" id="buy-and-scratch-btn">
                        Comprar e Raspar (R$ 1,00)
                    </button>
                </div>

                <!-- Game Board -->
                <div id="game-board" class="grid grid-cols-3 gap-2 p-2 bgaff md:min-w-[580px] md:min-h-[530px]"
                     style="width: 580px; height: 530px; display: none;">
                    <?php
                        $prizes = [
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/2_reais.png', 'text' => 'R$2,00'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/iphone12_white_12897651.webp', 'text' => 'Iphone'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/100_reais.jpg', 'text' => 'R$100,00'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/fone_bluetooth_c2050b7a.webp', 'text' => 'FONE'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/10_reais.png', 'text' => 'R$10,00'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/50_reais.png', 'text' => 'R$50,00'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/15_reais_5fbfe586.png', 'text' => 'R$15,00'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/5_reais.png', 'text' => 'R$5,00'],
                            ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/dinheiro_859419ed.webp', 'text' => 'R$1000,00']
                        ];
                    ?>
                    <?php for($i = 1; $i <= 9; $i++): ?>
                        <div class="scratch-position flex flex-col items-center justify-center min-h-[90px] bg-base rounded-md transition-all"
                             data-position="<?php echo e($i); ?>"
                             id="position-<?php echo e($i); ?>">
                            <div class="prize-display">
                                <img src="<?php echo e($prizes[$i-1]['image']); ?>" alt="premio" class="w-[40px] h-[40px] object-contain mb-1">
                                <span class="text-white text-xs font-semibold text-center"><?php echo e($prizes[$i-1]['text']); ?></span>
                            </div>
                            <div class="result-display hidden text-center">
                                <span class="symbol-content text-2xl text-white">?</span>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>

                <!-- Scratch Canvas Layer -->
                <canvas id="scratch-canvas"
                        class="scratch-area absolute top-0 left-0 z-10"
                        width="580"
                        height="530"
                        style="width: 580px; height: 530px; opacity: 1; cursor: none; display: none;">
                </canvas>

                <!-- Custom Cursor -->
                <div class="custom-cursor hidden md:block" style="left: 0px; top: 0px; display: none;">
                    <div class="coin-cursor"></div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-center gap-2 mt-2 md:max-w-[570px] md:min-w-[570px] min-w-[340px] mx-auto">
                <button id="play-btn" class="bgbotaop w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>JOGAR</span>
                </button>
                <div class="w-full rounded-lg font-bold bgaff h-[45px] md:min-h-[60px] flex flex-col items-center p-2">
                    <p class="textodesempenho text-xs">SEU SALDO</p>
                    <span class="text-amber-400 text-sm">R$ <span id="user-balance"><?php echo e(number_format(auth()->user()->balance, 2, ',', '.')); ?></span></span>
                </div>
            </div>

            <!-- Hidden buttons for game states -->
            <div class="flex items-center justify-center gap-2 mt-2 md:max-w-[570px] md:min-w-[570px] min-w-[340px] mx-auto" id="game-buttons" style="display: none;">
                <button id="reveal-all-btn" class="bg-[#d97706] w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center" style="display: none;">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span>REVELAR</span>
                </button>

                <button id="play-again-btn" class="bgbotaop w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center" style="display: none;">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <span>JOGAR NOVAMENTE</span>
                </button>
            </div>

            <!-- Result Message -->
            <div id="game-result" class="text-center text-white mt-2 bgaff rounded-lg mb-3 p-3 w-[92%] mx-auto md:max-w-[570px] hidden">
                <p>😢 Não foi dessa vez</p>
                <p class="text-white text-center uppercase blink-animation">Mas não desanime, a sorte pode estar na próxima raspadinha!</p>
            </div>
        </div>

        <!-- Sidebar seguindo o layout fornecido -->
        <div class="flex flex-col w-fit mx-auto mt-5 md:mt-[0px]">
            <!-- Banner promocional -->
            <div class="border bordanew p-4 rounded-md bgnewone">
                <img class="rounded-md w-[305px] h-[450px] md:max-h-[380px] md:max-w-[250px]"
                     src="https://worldgamesbr.com.br/wp-content/uploads/2025/07/banner-game.png">
            </div>

            <!-- Atalhos do Teclado -->
            <div class="hidden md:block bgnewone rounded-md p-1 flex flex-col mt-3 border bordanew">
                <div class="w-full flex items-center justify-center">
                    <span class="uppercase text-white font-bold text-lg text-center">Atalhos do Teclado</span>
                </div>
                <div class="flex items-center bgaff rounded-md w-full h-[40px] p-1 mt-2">
                    <span class="bg-gray-700 text-white px-3 py-1 rounded font-mono mr-2 min-w-[40px] text-center">R</span>
                    <span class="text-sm textodesempenho">Revelar tudo</span>
                </div>
                <div class="flex items-center bgaff rounded-md w-full h-[40px] p-1 mt-1">
                    <span class="bg-gray-700 text-white px-3 py-1 rounded font-mono mr-2 min-w-[40px] text-center">Espaço</span>
                    <span class="text-sm textodesempenho">Jogar/Comprar</span>
                </div>
                <div class="flex items-center bgaff rounded-md w-full h-[40px] p-1 mt-1">
                    <span class="bg-gray-700 text-white px-3 py-1 rounded font-mono mr-2 min-w-[40px] text-center">Enter</span>
                    <span class="text-sm textodesempenho">Confirmar</span>
                </div>
                <div class="flex items-center bgaff rounded-md w-full h-[40px] p-1 mt-1">
                    <span class="bg-gray-700 text-white px-3 py-1 rounded font-mono mr-2 min-w-[40px] text-center">Esc</span>
                    <span class="text-sm textodesempenho">Cancelar</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const gameBoard = document.getElementById('game-board');
    const playBtn = document.getElementById('play-btn');
    const buyAndScratchBtn = document.getElementById('buy-and-scratch-btn');
    const initialOverlay = document.getElementById('initial-overlay');
    const positions = document.querySelectorAll('.scratch-position');
    const userBalance = document.getElementById('user-balance');

    let gameStarted = false;
    let gameCompleted = <?php echo e($game->status === 'completed' ? 'true' : 'false'); ?>;
    let gameData = null;
    let canvas, ctx;
    let isDrawing = false;
    let scratchedPercentage = 0;

    // Event listeners para os botões
    if (buyAndScratchBtn) {
        buyAndScratchBtn.addEventListener('click', startGame);
    }
    if (playBtn) {
        playBtn.addEventListener('click', startGame);
    }

    // Start game
    async function startGame() {
        if (gameStarted) return;

        try {
            const response = await fetch('<?php echo e(route("games.scratch.start", $game->id)); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                gameData = data.game_data;
                gameStarted = true;

                // Update balance
                document.getElementById('user-balance').textContent = parseFloat(data.new_balance).toFixed(2).replace('.', ',');

                // Hide initial overlay and show game board
                if (initialOverlay) {
                    initialOverlay.style.display = 'none';
                }
                if (gameBoard) {
                    gameBoard.style.display = 'grid';
                }

                // Setup scratch canvas and game board
                setupScratchCanvas();
                setupGameBoard();
            } else {
                alert(data.message || 'Erro ao iniciar o jogo');
            }
        } catch (error) {
            console.error('Error starting game:', error);
            alert('Erro ao conectar com o servidor');
        }
    }

    function setupGameBoard() {
        positions.forEach((position, index) => {
            const positionNumber = index + 1;
            const symbol = gameData.symbols[positionNumber - 1];

            // Set the symbol content
            const symbolContent = position.querySelector('.symbol-content');
            if (symbolContent) {
                symbolContent.textContent = symbol;
            }
        });
    }

    function revealResults() {
        // Change background to green and show actual results
        const gameBoard = document.getElementById('game-board');
        gameBoard.style.background = 'linear-gradient(135deg, #10b981, #059669)';
        gameBoard.classList.remove('bgaff');

        // Show actual game symbols instead of prizes
        positions.forEach((position, index) => {
            const prizeDisplay = position.querySelector('.prize-display');
            const resultDisplay = position.querySelector('.result-display');
            const symbol = gameData.symbols[index];

            // Hide prize display
            if (prizeDisplay) {
                prizeDisplay.classList.add('hidden');
            }

            // Show result with actual symbol
            if (resultDisplay) {
                resultDisplay.classList.remove('hidden');
                const symbolContent = resultDisplay.querySelector('.symbol-content');
                if (symbolContent) {
                    symbolContent.textContent = symbol;
                    symbolContent.className = 'symbol-content text-4xl text-white font-bold';
                }
            }

            // Change position background to solid green
            position.style.background = '#10b981';
            position.style.border = '2px solid #1e40af';
        });

        // Show game result footer
        const gameResult = document.getElementById('game-result');
        if (gameResult) {
            gameResult.classList.remove('hidden');

            // Check if won
            const won = checkWinCondition();

            if (won && gameData && gameData.prize_amount > 0) {
                gameResult.innerHTML = `
                    <p>🎉 PARABÉNS! VOCÊ GANHOU!</p>
                    <p class="text-white text-center uppercase blink-animation">PRÊMIO: R$ ${gameData.prize_amount.toFixed(2).replace('.', ',')}</p>
                `;
            } else {
                gameResult.innerHTML = `
                    <p>😢 Não foi dessa vez</p>
                    <p class="text-white text-center uppercase blink-animation">Mas não desanime, a sorte pode estar na próxima raspadinha!</p>
                `;
            }
        }
    }

    function setupScratchCanvas() {
        canvas = document.getElementById('scratch-canvas');
        if (!canvas) return;

        ctx = canvas.getContext('2d');

        // Show canvas
        canvas.style.display = 'block';

        // Set canvas size to match game board
        canvas.width = 580;
        canvas.height = 530;
        canvas.style.width = '580px';
        canvas.style.height = '530px';

        // Draw scratch surface with "RASPE AQUI" background
        drawScratchSurface();

        // Set up scratch events
        setupScratchEvents();
    }

    function drawScratchSurface() {
        // Fill with gray scratch surface
        ctx.fillStyle = '#9CA3AF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add "RASPE AQUI" text
        ctx.fillStyle = '#374151';
        ctx.font = 'bold 32px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('RASPE AQUI', canvas.width / 2, canvas.height / 2 - 30);

        // Add arrows and hand cursor indication
        ctx.font = 'bold 24px Arial';
        ctx.fillText('← → ↑ ↓', canvas.width / 2, canvas.height / 2 + 20);

        // Add texture/noise for more realistic look
        for (let i = 0; i < 1000; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const opacity = Math.random() * 0.3;
            ctx.fillStyle = `rgba(55, 65, 81, ${opacity})`;
            ctx.fillRect(x, y, 1, 1);
        }
    }

    function setupScratchEvents() {
        // Mouse events
        canvas.addEventListener('mousedown', startScratch);
        canvas.addEventListener('mousemove', scratch);
        canvas.addEventListener('mouseup', stopScratch);
        canvas.addEventListener('mouseleave', stopScratch);

        // Touch events for mobile
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopScratch);

        // Prevent context menu
        canvas.addEventListener('contextmenu', e => e.preventDefault());

        // Custom cursor events
        setupCustomCursor();
    }

    function setupCustomCursor() {
        const customCursor = document.querySelector('.custom-cursor');
        if (!customCursor) return;

        canvas.addEventListener('mouseenter', function() {
            customCursor.style.display = 'block';
        });

        canvas.addEventListener('mouseleave', function() {
            customCursor.style.display = 'none';
        });

        canvas.addEventListener('mousemove', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            customCursor.style.left = (rect.left + x) + 'px';
            customCursor.style.top = (rect.top + y) + 'px';
        });

        // Add scratching animation when mouse is down
        canvas.addEventListener('mousedown', function() {
            const coin = customCursor.querySelector('.coin-cursor');
            if (coin) {
                coin.style.transform = 'scale(0.9) rotate(15deg)';
                coin.style.boxShadow = '0 0 15px rgba(255, 215, 0, 0.9), inset 0 2px 4px rgba(255, 255, 255, 0.4)';
            }
        });

        canvas.addEventListener('mouseup', function() {
            const coin = customCursor.querySelector('.coin-cursor');
            if (coin) {
                coin.style.transform = 'scale(1) rotate(0deg)';
                coin.style.boxShadow = '0 0 10px rgba(255, 215, 0, 0.6), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)';
            }
        });
    }

    function startScratch(e) {
        isDrawing = true;
        scratch(e);
    }

    function scratch(e) {
        if (!isDrawing) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        scratchAt(x, y);
    }

    function handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        if (!touch) return;

        const rect = canvas.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;

        if (e.type === 'touchstart') {
            isDrawing = true;
        }

        if (isDrawing) {
            scratchAt(x, y);
        }
    }

    function stopScratch() {
        isDrawing = false;
    }

    function scratchAt(x, y) {
        // Set composite operation to "destination-out" to erase
        ctx.globalCompositeOperation = 'destination-out';
        ctx.beginPath();
        ctx.arc(x, y, 25, 0, 2 * Math.PI);
        ctx.fill();

        // Add some scratch particles effect
        for (let i = 0; i < 3; i++) {
            const offsetX = (Math.random() - 0.5) * 10;
            const offsetY = (Math.random() - 0.5) * 10;
            ctx.beginPath();
            ctx.arc(x + offsetX, y + offsetY, Math.random() * 8 + 2, 0, 2 * Math.PI);
            ctx.fill();
        }

        // Reset composite operation
        ctx.globalCompositeOperation = 'source-over';

        // Add coin rotation effect during scratching
        const customCursor = document.querySelector('.custom-cursor');
        const coin = customCursor?.querySelector('.coin-cursor');
        if (coin && isDrawing) {
            const rotation = Math.random() * 30 - 15; // Random rotation between -15 and 15 degrees
            coin.style.transform = `scale(0.95) rotate(${rotation}deg)`;

            // Reset rotation after a short delay
            setTimeout(() => {
                if (coin) {
                    coin.style.transform = 'scale(1) rotate(0deg)';
                }
            }, 100);
        }

        // Check scratch percentage
        checkScratchProgress();
    }

    function checkWinCondition() {
        if (!gameData || !gameData.symbols) return false;

        const symbols = gameData.symbols;

        // Check rows
        for (let row = 0; row < 3; row++) {
            const start = row * 3;
            if (symbols[start] === symbols[start + 1] && symbols[start + 1] === symbols[start + 2]) {
                return true;
            }
        }

        // Check columns
        for (let col = 0; col < 3; col++) {
            if (symbols[col] === symbols[col + 3] && symbols[col + 3] === symbols[col + 6]) {
                return true;
            }
        }

        // Check diagonals
        if (symbols[0] === symbols[4] && symbols[4] === symbols[8]) {
            return true;
        }
        if (symbols[2] === symbols[4] && symbols[4] === symbols[6]) {
            return true;
        }

        return false;
    }

    function checkScratchProgress() {
        // Sample pixels to check how much has been scratched
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const pixels = imageData.data;
        let transparentPixels = 0;

        // Check every 10th pixel for performance
        for (let i = 3; i < pixels.length; i += 40) {
            if (pixels[i] === 0) { // Alpha channel is 0 (transparent)
                transparentPixels++;
            }
        }

        scratchedPercentage = (transparentPixels / (pixels.length / 40)) * 100;

        // If 30% or more is scratched, reveal the game
        if (scratchedPercentage >= 30) {
            revealGame();
        }
    }

    function revealGame() {
        if (gameCompleted) return;

        // Hide canvas
        canvas.style.display = 'none';

        // Show game buttons container and reveal button
        const gameButtons = document.getElementById('game-buttons');
        const revealBtn = document.getElementById('reveal-all-btn');

        if (gameButtons) {
            gameButtons.style.display = 'flex';
        }
        if (revealBtn) {
            revealBtn.style.display = 'flex';
        }
    }



    async function endGame() {
        if (gameCompleted) return;
        gameCompleted = true;

        // Clear the scratch canvas to reveal results
        if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // Show the results
        revealResults();

        // Determine if won based on symbols
        const won = checkWinCondition();

        try {
            const response = await fetch('<?php echo e(route("games.scratch.end", $game->id)); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    scratched_positions: [1,2,3,4,5,6,7,8,9], // All positions revealed
                    won: won
                })
            });

            const data = await response.json();

            if (data.success) {
                // Update balance
                document.getElementById('user-balance').textContent = 'R$ ' + parseFloat(data.new_balance).toFixed(2).replace('.', ',');

                // Update game data with prize amount
                if (data.prize_amount) {
                    gameData.prize_amount = data.prize_amount;

                    // Update the result display if won
                    if (won) {
                        const gameResult = document.getElementById('game-result');
                        if (gameResult) {
                            gameResult.innerHTML = `
                                <div class="flex items-center justify-center mb-2">
                                    <span class="text-yellow-400 text-lg mr-2">🎉</span>
                                    <span class="text-white font-bold">PARABÉNS! VOCÊ GANHOU!</span>
                                </div>
                                <p class="text-yellow-400 text-sm font-bold">PRÊMIO: R$ ${parseFloat(data.prize_amount).toFixed(2).replace('.', ',')}</p>
                            `;
                        }
                    }
                }

                // Hide reveal button and show play again button
                const revealBtn = document.getElementById('reveal-all-btn');
                const playAgainBtn = document.getElementById('play-again-btn');
                const gameButtons = document.getElementById('game-buttons');

                if (revealBtn) revealBtn.style.display = 'none';
                if (playAgainBtn) playAgainBtn.style.display = 'flex';
                if (gameButtons) gameButtons.style.display = 'flex';
            } else {
                console.error('Error ending game:', data.message);
            }
        } catch (error) {
            console.error('Error ending game:', error);
        }
    }

    function checkWinCondition() {
        // Count symbol occurrences
        const symbolCounts = {};
        gameData.symbols.forEach(symbol => {
            symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
        });

        // Check if any symbol appears 3 or more times
        return Object.values(symbolCounts).some(count => count >= 3);
    }

    function resetGame() {
        gameStarted = false;
        gameData = null;
        gameCompleted = false;
        scratchedPercentage = 0;

        // Reset all positions - show prizes again, hide results
        positions.forEach(position => {
            const prizeDisplay = position.querySelector('.prize-display');
            const resultDisplay = position.querySelector('.result-display');
            const symbolContent = position.querySelector('.symbol-content');

            if (prizeDisplay && resultDisplay) {
                prizeDisplay.classList.remove('hidden');
                resultDisplay.classList.add('hidden');
            }

            if (symbolContent) {
                symbolContent.textContent = '?';
            }

            // Reset position background
            position.style.background = '';
            position.style.border = '';
        });

        // Reset game board background
        if (gameBoard) {
            gameBoard.style.background = '';
            gameBoard.classList.add('bgaff');
            gameBoard.style.display = 'none';
        }

        // Show initial overlay again
        if (initialOverlay) {
            initialOverlay.style.display = 'flex';
        }

        // Hide canvas
        const canvas = document.getElementById('scratch-canvas');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // Hide result footer and buttons
        const gameResult = document.getElementById('game-result');
        const revealBtn = document.getElementById('reveal-all-btn');
        const playAgainBtn = document.getElementById('play-again-btn');
        const gameButtons = document.getElementById('game-buttons');

        if (gameResult) gameResult.classList.add('hidden');
        if (revealBtn) revealBtn.style.display = 'none';
        if (playAgainBtn) playAgainBtn.style.display = 'none';
        if (gameButtons) gameButtons.style.display = 'none';
    }

    // Event listeners adicionais
    const revealAllBtn = document.getElementById('reveal-all-btn');
    if (revealAllBtn) {
        revealAllBtn.addEventListener('click', function() {
            endGame();
        });
    }

    const playAgainBtn = document.getElementById('play-again-btn');
    if (playAgainBtn) {
        playAgainBtn.addEventListener('click', function() {
            // Hide result footer
            const gameResult = document.getElementById('game-result');
            if (gameResult) {
                gameResult.classList.add('hidden');
            }

            // Reset game
            resetGame();
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        switch(e.key.toLowerCase()) {
            case ' ':
                e.preventDefault();
                if (!gameStarted) {
                    startGame();
                } else if (gameCompleted) {
                    resetGame();
                }
                break;
            case 'enter':
                if (!gameStarted) {
                    startGame();
                } else if (gameCompleted) {
                    resetGame();
                }
                break;
            case 'r':
                if (gameStarted && !gameCompleted) {
                    endGame();
                }
                break;
            case 'escape':
                if (gameStarted && !gameCompleted) {
                    // Cancelar jogo atual
                    resetGame();
                }
                break;
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Classes específicas do layout original */
    .bgaff {
        background: rgba(0, 0, 0, 0.3);
    }

    .bg-base {
        background: rgba(0, 0, 0, 0.4);
    }

    .bgbotaop {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .textodesempenho {
        color: #9ca3af;
    }

    /* Novas classes do layout fornecido */
    .bgnewone {
        background: linear-gradient(135deg, #1e3a8a, #1e40af);
    }

    .bordanew {
        border: 2px solid #3b82f6;
    }

    .custom-cursor {
        pointer-events: none;
        position: absolute;
        width: 40px;
        height: 40px;
        z-index: 1000;
        transform: translate(-50%, -50%);
        transition: opacity 0.2s ease;
    }

    .coin-cursor {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%);
        border: 3px solid #b8860b;
        border-radius: 50%;
        box-shadow:
            0 0 10px rgba(255, 215, 0, 0.6),
            inset 0 2px 4px rgba(255, 255, 255, 0.3),
            inset 0 -2px 4px rgba(0, 0, 0, 0.2);
        position: relative;
        animation: coinGlow 2s ease-in-out infinite alternate;
    }

    .coin-cursor::before {
        content: 'R$';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 12px;
        color: #8b4513;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
    }

    .coin-cursor::after {
        content: '';
        position: absolute;
        top: 3px;
        left: 8px;
        width: 8px;
        height: 8px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        blur: 1px;
    }

    @keyframes coinGlow {
        0% {
            box-shadow:
                0 0 10px rgba(255, 215, 0, 0.6),
                inset 0 2px 4px rgba(255, 255, 255, 0.3),
                inset 0 -2px 4px rgba(0, 0, 0, 0.2);
        }
        100% {
            box-shadow:
                0 0 20px rgba(255, 215, 0, 0.8),
                inset 0 2px 4px rgba(255, 255, 255, 0.4),
                inset 0 -2px 4px rgba(0, 0, 0, 0.3);
        }
    }

    .scratch-area {
        cursor: none !important;
    }

    .scratch-area:hover + .custom-cursor {
        opacity: 1;
    }

    .blink-animation {
        animation: blink 2s infinite;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.5; }
    }

    .scratch-position {
        transition: all 0.3s ease;
    }

    .scratch-position:hover {
        transform: scale(1.05);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\raspou-pix\resources\views/games/scratch-game.blade.php ENDPATH**/ ?>