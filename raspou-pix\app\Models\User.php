<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_blocked',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_blocked' => 'boolean',
    ];

    public function games()
    {
        return $this->hasMany(Game::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function achievements()
    {
        return $this->belongsToMany(Achievement::class, 'user_achievements')
                    ->withPivot('earned_at')
                    ->withTimestamps();
    }

    public function userAchievements()
    {
        return $this->hasMany(UserAchievement::class);
    }

    /**
     * Check and award new achievements to the user
     */
    public function checkAchievements()
    {
        $achievements = Achievement::where('is_active', true)->get();
        $newAchievements = [];

        foreach ($achievements as $achievement) {
            if ($achievement->awardTo($this)) {
                $newAchievements[] = $achievement;
            }
        }

        return $newAchievements;
    }

    public function getBalanceAttribute()
    {
        return $this->transactions()
            ->where('status', 'completed')
            ->where(function($query) {
                $query->where('type', 'deposit')
                      ->orWhere('type', 'prize_win');
            })
            ->sum('amount') -
            $this->transactions()
            ->where('status', 'completed')
            ->where(function($query) {
                $query->where('type', 'withdrawal')
                      ->orWhere('type', 'game_purchase');
            })
            ->sum('amount');
    }

    public function getFormattedBalanceAttribute()
    {
        return 'R$ ' . number_format($this->balance, 2, ',', '.');
    }
}
