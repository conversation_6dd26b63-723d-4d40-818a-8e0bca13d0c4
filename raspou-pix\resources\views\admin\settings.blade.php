@extends('admin.layout')

@section('title', 'Configurações')
@section('page-title', 'Configurações')
@section('page-description', 'Configurações do sistema de raspadinhas')

@section('content')
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Game Settings -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center mb-6">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-gamepad text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-800">Configurações do Jogo</h3>
                <p class="text-sm text-gray-600">Ajuste os parâmetros das raspadinhas</p>
            </div>
        </div>

        <form method="POST" action="{{ route('admin.settings.update') }}">
            @csrf
            <div class="space-y-4">
                <div>
                    <label for="game_cost" class="block text-sm font-medium text-gray-700 mb-2">
                        Custo por Jogo (R$)
                    </label>
                    <input type="number" 
                           id="game_cost" 
                           name="game_cost" 
                           step="0.01" 
                           min="0.01"
                           value="{{ session('game_cost', 1.00) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Valor que o usuário paga para jogar</p>
                </div>

                <div>
                    <label for="max_prize" class="block text-sm font-medium text-gray-700 mb-2">
                        Prêmio Máximo (R$)
                    </label>
                    <input type="number" 
                           id="max_prize" 
                           name="max_prize" 
                           step="0.01" 
                           min="0"
                           value="{{ session('max_prize', 1000.00) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Maior prêmio possível</p>
                </div>

                <div>
                    <label for="win_probability" class="block text-sm font-medium text-gray-700 mb-2">
                        Probabilidade de Vitória (%)
                    </label>
                    <input type="number" 
                           id="win_probability" 
                           name="win_probability" 
                           step="0.1" 
                           min="0" 
                           max="100"
                           value="{{ session('win_probability', 15.0) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Chance de o jogador ganhar</p>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200">
                        <i class="fas fa-save mr-2"></i>
                        Salvar Configurações
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Prize Distribution -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center mb-6">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-trophy text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-800">Distribuição de Prêmios</h3>
                <p class="text-sm text-gray-600">Configure os valores dos prêmios</p>
            </div>
        </div>

        <div class="space-y-4">
            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-coins text-yellow-600 mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">Prêmio Pequeno</p>
                        <p class="text-sm text-gray-600">R$ 10,00 - R$ 50,00</p>
                    </div>
                </div>
                <span class="text-sm font-medium text-yellow-600">60%</span>
            </div>

            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-gem text-blue-600 mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">Prêmio Médio</p>
                        <p class="text-sm text-gray-600">R$ 100,00 - R$ 500,00</p>
                    </div>
                </div>
                <span class="text-sm font-medium text-blue-600">30%</span>
            </div>

            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-crown text-purple-600 mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">Prêmio Grande</p>
                        <p class="text-sm text-gray-600">R$ 1.000,00+</p>
                    </div>
                </div>
                <span class="text-sm font-medium text-purple-600">10%</span>
            </div>
        </div>

        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 class="font-medium text-gray-800 mb-2">Símbolos Disponíveis</h4>
            <div class="grid grid-cols-3 gap-2">
                <div class="text-center p-2 bg-white rounded border">
                    <div class="text-2xl mb-1">💰</div>
                    <div class="text-xs text-gray-600">Dinheiro</div>
                </div>
                <div class="text-center p-2 bg-white rounded border">
                    <div class="text-2xl mb-1">🎁</div>
                    <div class="text-xs text-gray-600">Presente</div>
                </div>
                <div class="text-center p-2 bg-white rounded border">
                    <div class="text-2xl mb-1">💎</div>
                    <div class="text-xs text-gray-600">Diamante</div>
                </div>
                <div class="text-center p-2 bg-white rounded border">
                    <div class="text-2xl mb-1">🍀</div>
                    <div class="text-xs text-gray-600">Trevo</div>
                </div>
                <div class="text-center p-2 bg-white rounded border">
                    <div class="text-2xl mb-1">⭐</div>
                    <div class="text-xs text-gray-600">Estrela</div>
                </div>
                <div class="text-center p-2 bg-white rounded border">
                    <div class="text-2xl mb-1">🎰</div>
                    <div class="text-xs text-gray-600">Slot</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="mt-6 bg-white rounded-lg shadow-md p-6">
    <div class="flex items-center mb-6">
        <div class="p-3 rounded-full bg-gray-100 text-gray-600">
            <i class="fas fa-info-circle text-xl"></i>
        </div>
        <div class="ml-4">
            <h3 class="text-lg font-semibold text-gray-800">Informações do Sistema</h3>
            <p class="text-sm text-gray-600">Detalhes técnicos e estatísticas</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ PHP_VERSION }}</div>
            <div class="text-sm text-gray-600">Versão do PHP</div>
        </div>
        <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ app()->version() }}</div>
            <div class="text-sm text-gray-600">Laravel</div>
        </div>
        <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ number_format(memory_get_usage(true) / 1024 / 1024, 1) }}MB</div>
            <div class="text-sm text-gray-600">Uso de Memória</div>
        </div>
    </div>
</div>

<!-- Backup & Maintenance -->
<div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center mb-4">
            <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                <i class="fas fa-database text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-800">Backup</h3>
                <p class="text-sm text-gray-600">Gerenciar backups do sistema</p>
            </div>
        </div>
        <div class="space-y-3">
            <button class="w-full bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 transition duration-200">
                <i class="fas fa-download mr-2"></i>
                Fazer Backup Agora
            </button>
            <p class="text-xs text-gray-500">Último backup: Nunca</p>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center mb-4">
            <div class="p-3 rounded-full bg-red-100 text-red-600">
                <i class="fas fa-tools text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-800">Manutenção</h3>
                <p class="text-sm text-gray-600">Ferramentas de manutenção</p>
            </div>
        </div>
        <div class="space-y-3">
            <button class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition duration-200">
                <i class="fas fa-broom mr-2"></i>
                Limpar Cache
            </button>
            <button class="w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition duration-200">
                <i class="fas fa-sync mr-2"></i>
                Otimizar Banco
            </button>
        </div>
    </div>
</div>
@endsection
