@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON>ores - Ra<PERSON><PERSON> Pix')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-white mb-4">
            <i class="fas fa-trophy mr-3 text-yellow-500"></i>
            Nossos Ganhadores
        </h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
            Confira quem já ganhou prêmios incríveis nas nossas raspadinhas! 
            Você pode ser o próximo!
        </p>
    </div>

    @if($winners->count() > 0)
        <!-- Lista de Ganhadores -->
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($winners as $winner)
                <div class="card p-6 hover:shadow-lg transition-shadow">
                    <!-- Layout principal com grid -->
                    <div class="grid grid-cols-3 gap-4">
                        <!-- Imagem do jogo (1/3 do espaço) -->
                        <div class="col-span-1">
                            <div class="relative rounded-lg overflow-hidden h-full flex items-center justify-center" style="min-height: 100px; max-height: 140px; background-color: #1a2234;">
                                <img src="{{ $winner->scratchCard->image_url ?? 'https://files.raspoupix.com/premio-dinheiro/100_reais.png' }}" 
                                     alt="{{ $winner->scratchCard->name }}" 
                                     class="h-full w-full object-contain"
                                     onerror="this.src='https://files.raspoupix.com/premio-dinheiro/100_reais.png'; this.onerror=null;">
                            </div>
                        </div>
                        
                        <!-- Informações do ganhador (2/3 do espaço) -->
                        <div class="col-span-2">
                            <!-- Badge do tipo de prêmio -->
                            <div class="flex justify-end mb-2">
                                <span class="badge bg-yellow-600 text-white text-xs px-3 py-1 rounded-full">
                                    <i class="fas fa-crown mr-1"></i> {{ $winner->scratchCard->name }}
                                </span>
                            </div>
                            
                            <!-- Dados do usuário -->
                            <p class="text-xs text-gray-400 mb-1">Ganhador</p>
                            <p class="font-bold text-white text-lg mb-2">
                                {{ substr($winner->user->name, 0, strpos($winner->user->name, ' ') ?: strlen($winner->user->name)) }} 
                                {{ substr($winner->user->name, strpos($winner->user->name, ' ') + 1, 1) }}.
                            </p>
                            
                            <!-- Informação do prêmio -->
                            <div class="mb-3">
                                <p class="text-xs text-gray-400 mb-1">Prêmio Ganho</p>
                                <p class="text-2xl font-bold text-green-500">
                                    R$ {{ number_format($winner->prize_amount, 2, ',', '.') }}
                                </p>
                            </div>
                            
                            <!-- Data do prêmio -->
                            <p class="text-xs text-gray-400">
                                <i class="far fa-calendar-alt mr-1"></i> 
                                {{ $winner->created_at->format('d/m/Y H:i') }}
                            </p>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Estatísticas -->
            <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="card p-6 text-center">
                    <i class="fas fa-users text-4xl text-blue-500 mb-4"></i>
                    <h3 class="text-2xl font-bold text-white mb-2">{{ $winners->count() }}</h3>
                    <p class="text-gray-400">Ganhadores Recentes</p>
                </div>
                <div class="card p-6 text-center">
                    <i class="fas fa-money-bill-wave text-4xl text-green-500 mb-4"></i>
                    <h3 class="text-2xl font-bold text-white mb-2">
                        R$ {{ number_format($winners->sum('prize_amount'), 2, ',', '.') }}
                    </h3>
                    <p class="text-gray-400">Total em Prêmios</p>
                </div>
                <div class="card p-6 text-center">
                    <i class="fas fa-trophy text-4xl text-yellow-500 mb-4"></i>
                    <h3 class="text-2xl font-bold text-white mb-2">
                        R$ {{ number_format($winners->max('prize_amount'), 2, ',', '.') }}
                    </h3>
                    <p class="text-gray-400">Maior Prêmio</p>
                </div>
            </div>
        </div>
    @else
        <!-- Nenhum ganhador ainda -->
        <div class="max-w-2xl mx-auto text-center">
            <div class="card p-8">
                <i class="fas fa-trophy text-6xl text-gray-600 mb-6"></i>
                <h2 class="text-2xl font-bold text-white mb-4">Ainda não temos ganhadores</h2>
                <p class="text-gray-400 mb-6">
                    Seja o primeiro a ganhar! Compre sua raspadinha e tente a sorte.
                </p>
                <a href="{{ route('scratch-cards.index') }}" class="btn-primary">
                    <i class="fas fa-play mr-2"></i>Jogar Agora
                </a>
            </div>
        </div>
    @endif

    <!-- Call to Action -->
    <div class="max-w-4xl mx-auto mt-12">
        <div class="bg-gradient-to-r from-purple-600 to-blue-600 p-8 rounded-lg text-center">
            <h2 class="text-3xl font-bold text-white mb-4">Você pode ser o próximo!</h2>
            <p class="text-xl text-gray-200 mb-6">
                Milhares de prêmios esperando por você. Comece a jogar agora!
            </p>
            <div class="space-x-4">
                <a href="{{ route('scratch-cards.index') }}" class="btn-primary">
                    <i class="fas fa-play mr-2"></i>Ver Raspadinhas
                </a>
                @guest
                    <a href="{{ route('register') }}" class="btn-secondary">
                        <i class="fas fa-user-plus mr-2"></i>Criar Conta
                    </a>
                @endguest
            </div>
        </div>
    </div>
</div>
@endsection
