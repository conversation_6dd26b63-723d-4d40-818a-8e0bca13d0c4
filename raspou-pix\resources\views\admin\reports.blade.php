@extends('admin.layout')

@section('title', 'Relatórios')
@section('page-title', 'Relatórios')
@section('page-description', 'Análises e relatórios detalhados')

@section('content')
<!-- Filter Section -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800">Filtros de Relatório</h3>
        <div class="flex items-center space-x-4">
            <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="7">Últimos 7 dias</option>
                <option value="30" selected>Últimos 30 dias</option>
                <option value="90">Últimos 90 dias</option>
                <option value="365">Último ano</option>
            </select>
            <input type="date" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <input type="date" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <i class="fas fa-search mr-2"></i>Aplicar
            </button>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Revenue Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Receita Diária</h3>
            <div class="flex items-center space-x-2">
                <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                <span class="text-sm text-gray-600">Receita</span>
                <span class="w-3 h-3 bg-red-500 rounded-full ml-4"></span>
                <span class="text-sm text-gray-600">Prêmios</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="revenueChart"></canvas>
        </div>
    </div>

    <!-- Games Chart -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Jogos por Dia</h3>
            <div class="flex items-center space-x-2">
                <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                <span class="text-sm text-gray-600">Total de Jogos</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="gamesChart"></canvas>
        </div>
    </div>
</div>

<!-- Daily Stats Table -->
<div class="bg-white rounded-lg shadow-md mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">Estatísticas Diárias</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jogos</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receita</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prêmios</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lucro</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taxa Vitória</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($dailyStats as $stat)
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {{ \Carbon\Carbon::parse($stat->date)->format('d/m/Y') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ number_format($stat->games_count) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        R$ {{ number_format($stat->total_cost, 2, ',', '.') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        R$ {{ number_format($stat->total_prizes, 2, ',', '.') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm {{ $stat->profit >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        R$ {{ number_format($stat->profit, 2, ',', '.') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ $stat->games_count > 0 ? number_format(($stat->total_prizes > 0 ? 1 : 0) * 100, 1) : 0 }}%
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                        Nenhum dado encontrado para o período selecionado
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- Monthly Summary -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Resumo Mensal</h3>
        <div class="space-y-4">
            @forelse($monthlyStats->take(6) as $stat)
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-800">
                        {{ \Carbon\Carbon::createFromDate($stat->year, $stat->month, 1)->format('M/Y') }}
                    </p>
                    <p class="text-sm text-gray-600">{{ number_format($stat->games_count) }} jogos</p>
                </div>
                <div class="text-right">
                    <p class="font-medium {{ $stat->profit >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        R$ {{ number_format($stat->profit, 2, ',', '.') }}
                    </p>
                    <p class="text-sm text-gray-600">lucro</p>
                </div>
            </div>
            @empty
            <p class="text-gray-500 text-center">Nenhum dado mensal disponível</p>
            @endforelse
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Análise de Performance</h3>
        <div class="space-y-4">
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">Crescimento</p>
                        <p class="text-sm text-gray-600">Comparado ao mês anterior</p>
                    </div>
                </div>
                <span class="text-lg font-bold text-blue-600">+12.5%</span>
            </div>

            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-users text-green-600 mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">Retenção</p>
                        <p class="text-sm text-gray-600">Usuários que voltaram</p>
                    </div>
                </div>
                <span class="text-lg font-bold text-green-600">68%</span>
            </div>

            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-trophy text-yellow-600 mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">Taxa de Vitória</p>
                        <p class="text-sm text-gray-600">Média geral</p>
                    </div>
                </div>
                <span class="text-lg font-bold text-yellow-600">15.2%</span>
            </div>

            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-coins text-purple-600 mr-3"></i>
                    <div>
                        <p class="font-medium text-gray-800">Ticket Médio</p>
                        <p class="text-sm text-gray-600">Valor por jogo</p>
                    </div>
                </div>
                <span class="text-lg font-bold text-purple-600">R$ 2,50</span>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: [
                @foreach($dailyStats->take(7) as $stat)
                '{{ \Carbon\Carbon::parse($stat->date)->format("d/m") }}',
                @endforeach
            ],
            datasets: [{
                label: 'Receita',
                data: [
                    @foreach($dailyStats->take(7) as $stat)
                    {{ $stat->total_cost }},
                    @endforeach
                ],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }, {
                label: 'Prêmios',
                data: [
                    @foreach($dailyStats->take(7) as $stat)
                    {{ $stat->total_prizes }},
                    @endforeach
                ],
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Games Chart
    const gamesCtx = document.getElementById('gamesChart').getContext('2d');
    new Chart(gamesCtx, {
        type: 'bar',
        data: {
            labels: [
                @foreach($dailyStats->take(7) as $stat)
                '{{ \Carbon\Carbon::parse($stat->date)->format("d/m") }}',
                @endforeach
            ],
            datasets: [{
                label: 'Jogos',
                data: [
                    @foreach($dailyStats->take(7) as $stat)
                    {{ $stat->games_count }},
                    @endforeach
                ],
                backgroundColor: '#10b981',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
@endsection
