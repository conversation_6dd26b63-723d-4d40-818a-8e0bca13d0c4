<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/raspadinhas', [HomeController::class, 'scratchCards'])->name('scratch-cards.index');
Route::get('/numeros-da-sorte', [HomeController::class, 'luckyNumbers'])->name('lucky-numbers');
Route::get('/vencedores', [HomeController::class, 'winners'])->name('winners');
Route::get('/ranking', function () { return view('leaderboard.index'); })->name('leaderboard');
Route::get('/conquistas', [App\Http\Controllers\AchievementController::class, 'index'])->name('achievements');

Route::middleware('auth')->group(function () {
    Route::post('/games/purchase/{scratchCard}', [GameController::class, 'purchase'])->name('games.purchase');
    Route::get('/games/{game}/play', [GameController::class, 'play'])->name('games.play');
    Route::post('/games/{game}/scratch', [GameController::class, 'scratch'])->name('games.scratch');
    Route::post('/games/{game}/start', [GameController::class, 'startGame'])->name('games.scratch.start');
    Route::post('/games/{game}/end', [GameController::class, 'endGame'])->name('games.scratch.end');
    Route::get('/carteira', [HomeController::class, 'wallet'])->name('wallet');
    Route::get('/perfil', [HomeController::class, 'profile'])->name('profile');

    // Statistics API routes
    Route::get('/api/stats/realtime', [App\Http\Controllers\StatsController::class, 'realTimeStats'])->name('stats.realtime');
    Route::get('/api/stats/user', [App\Http\Controllers\StatsController::class, 'userStats'])->name('stats.user');
    Route::get('/api/stats/leaderboard', [App\Http\Controllers\StatsController::class, 'leaderboard'])->name('stats.leaderboard');
    Route::get('/api/stats/history', [App\Http\Controllers\StatsController::class, 'gameHistory'])->name('stats.history');

    // Achievements API routes
    Route::get('/api/achievements', [App\Http\Controllers\AchievementController::class, 'getUserAchievements'])->name('api.achievements');
    Route::post('/api/achievements/check', [App\Http\Controllers\AchievementController::class, 'checkUserAchievements'])->name('api.achievements.check');

    // Admin routes
    Route::prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/users', [App\Http\Controllers\AdminController::class, 'users'])->name('users');
        Route::get('/games', [App\Http\Controllers\AdminController::class, 'games'])->name('games');
        Route::get('/reports', [App\Http\Controllers\AdminController::class, 'reports'])->name('reports');
        Route::get('/settings', [App\Http\Controllers\AdminController::class, 'settings'])->name('settings');

        // Admin actions
        Route::post('/settings/update', [App\Http\Controllers\AdminController::class, 'updateGameSettings'])->name('settings.update');
        Route::delete('/games/{game}', [App\Http\Controllers\AdminController::class, 'deleteGame'])->name('games.delete');
        Route::post('/users/{user}/block', [App\Http\Controllers\AdminController::class, 'blockUser'])->name('users.block');
        Route::post('/users/{user}/unblock', [App\Http\Controllers\AdminController::class, 'unblockUser'])->name('users.unblock');
    });
});

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login'])->name('login.post');
Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register'])->name('register.post');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
