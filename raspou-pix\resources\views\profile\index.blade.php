@extends('layouts.app')

@section('title', '<PERSON><PERSON> - Ra<PERSON> Pix')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">
            <i class="fas fa-user mr-3 text-blue-500"></i>
            Meu Perfil
        </h1>
        <p class="text-xl text-gray-300">
            Gerencie suas informações e acompanhe suas estatísticas
        </p>
    </div>

    <div class="max-w-6xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Informações do Perfil -->
            <div class="lg:col-span-1">
                <div class="card p-6">
                    <div class="text-center mb-6">
                        <div class="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-user text-3xl text-white"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-white mb-2">{{ $user->name }}</h2>
                        <p class="text-gray-400">{{ $user->email }}</p>
                        <p class="text-sm text-gray-500 mt-2">
                            Membro desde {{ $user->created_at->format('d/m/Y') }}
                        </p>
                    </div>

                    <!-- Saldo Atual -->
                    <div class="bg-gradient-to-r from-green-600 to-blue-600 p-4 rounded-lg text-center mb-6">
                        <p class="text-sm text-gray-200 mb-1">Saldo Atual</p>
                        <p class="text-2xl font-bold text-white">
                            R$ {{ number_format($user->balance, 2, ',', '.') }}
                        </p>
                    </div>

                    <!-- Ações Rápidas -->
                    <div class="space-y-3">
                        <a href="{{ route('wallet') }}" class="block w-full btn-primary text-center">
                            <i class="fas fa-wallet mr-2"></i>Ver Carteira
                        </a>
                        <button class="block w-full btn-secondary text-center">
                            <i class="fas fa-edit mr-2"></i>Editar Perfil
                        </button>
                        <form method="POST" action="{{ route('logout') }}" class="w-full">
                            @csrf
                            <button type="submit" class="block w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-sign-out-alt mr-2"></i>Sair
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Estatísticas e Atividades -->
            <div class="lg:col-span-2">
                <!-- Estatísticas -->
                <div class="card p-6 mb-8">
                    <h3 class="text-2xl font-bold text-white mb-6">
                        <i class="fas fa-chart-bar mr-2"></i>Suas Estatísticas
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-gray-800 rounded-lg">
                            <i class="fas fa-gamepad text-3xl text-blue-500 mb-3"></i>
                            <h4 class="text-2xl font-bold text-white mb-1">{{ $totalGames }}</h4>
                            <p class="text-gray-400">Jogos Jogados</p>
                        </div>
                        <div class="text-center p-4 bg-gray-800 rounded-lg">
                            <i class="fas fa-trophy text-3xl text-yellow-500 mb-3"></i>
                            <h4 class="text-2xl font-bold text-white mb-1">{{ $totalWins }}</h4>
                            <p class="text-gray-400">Vitórias</p>
                        </div>
                        <div class="text-center p-4 bg-gray-800 rounded-lg">
                            <i class="fas fa-money-bill-wave text-3xl text-green-500 mb-3"></i>
                            <h4 class="text-2xl font-bold text-white mb-1">
                                R$ {{ number_format($totalPrizes, 2, ',', '.') }}
                            </h4>
                            <p class="text-gray-400">Total em Prêmios</p>
                        </div>
                    </div>

                    <!-- Taxa de Vitória -->
                    <div class="mt-6 p-4 bg-gray-800 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-400">Taxa de Vitória</span>
                            <span class="text-white font-bold">
                                {{ $totalGames > 0 ? number_format(($totalWins / $totalGames) * 100, 1) : 0 }}%
                            </span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full" 
                                 style="width: {{ $totalGames > 0 ? ($totalWins / $totalGames) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>

                <!-- Jogos Recentes -->
                <div class="card p-6">
                    <h3 class="text-2xl font-bold text-white mb-6">
                        <i class="fas fa-history mr-2"></i>Jogos Recentes
                    </h3>

                    @php
                        $recentGames = $user->games()->with('scratchCard')->orderBy('created_at', 'desc')->limit(5)->get();
                    @endphp

                    @if($recentGames->count() > 0)
                        <div class="space-y-4">
                            @foreach($recentGames as $game)
                            <div class="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-ticket-alt text-purple-500"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-white font-semibold">{{ $game->scratchCard->name }}</h4>
                                        <p class="text-gray-400 text-sm">{{ $game->created_at->format('d/m/Y H:i') }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    @if($game->prize_amount > 0)
                                        <p class="text-green-500 font-bold">
                                            +R$ {{ number_format($game->prize_amount, 2, ',', '.') }}
                                        </p>
                                        <span class="badge bg-green-600 text-white text-xs">
                                            <i class="fas fa-trophy mr-1"></i>Ganhou
                                        </span>
                                    @else
                                        <p class="text-gray-400">Sem prêmio</p>
                                        <span class="badge bg-gray-600 text-white text-xs">
                                            <i class="fas fa-times mr-1"></i>Perdeu
                                        </span>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <div class="text-center mt-6">
                            <a href="{{ route('wallet') }}" class="btn-secondary">
                                <i class="fas fa-history mr-2"></i>Ver Histórico Completo
                            </a>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-gamepad text-6xl text-gray-600 mb-4"></i>
                            <h4 class="text-xl font-bold text-white mb-2">Nenhum jogo ainda</h4>
                            <p class="text-gray-400 mb-4">Comece a jogar para ver seu histórico aqui.</p>
                            <a href="{{ route('scratch-cards.index') }}" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>Jogar Agora
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
