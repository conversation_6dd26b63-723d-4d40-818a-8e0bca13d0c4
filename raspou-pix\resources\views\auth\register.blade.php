@extends('layouts.app')

@section('title', 'Cadastrar - Raspou Pix')

@section('content')
<div class="max-w-md mx-auto">
    <div class="bg-gray-800 p-8 rounded-lg shadow-lg">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                🎰 Cadastrar
            </h1>
            <p class="text-gray-300 mt-2">Crie sua conta e ganhe R$ 10 de bônus!</p>
        </div>

        @if ($errors->any())
            <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
                <ul class="list-disc list-inside">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('register.post') }}">
            @csrf

            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">
                    Nome <PERSON>mpleto
                </label>
                <input type="text"
                       id="name"
                       name="name"
                       value="{{ old('name') }}"
                       required
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                       placeholder="Seu nome completo">
            </div>

            <div class="mb-6">
                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                    E-mail
                </label>
                <input type="email"
                       id="email"
                       name="email"
                       value="{{ old('email') }}"
                       required
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                       placeholder="<EMAIL>">
            </div>

            <div class="mb-6">
                <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                    Senha
                </label>
                <input type="password"
                       id="password"
                       name="password"
                       required
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                       placeholder="Mínimo 6 caracteres">
            </div>

            <div class="mb-6">
                <label for="password_confirmation" class="block text-sm font-medium text-gray-300 mb-2">
                    Confirmar Senha
                </label>
                <input type="password"
                       id="password_confirmation"
                       name="password_confirmation"
                       required
                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                       placeholder="Confirme sua senha">
            </div>

            <div class="mb-6">
                <label class="flex items-start">
                    <input type="checkbox" name="terms" required class="mt-1 rounded border-gray-600 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm text-gray-300">
                        Eu concordo com os
                        <a href="#" class="text-purple-400 hover:text-purple-300">Termos de Uso</a>
                        e
                        <a href="#" class="text-purple-400 hover:text-purple-300">Política de Privacidade</a>
                    </span>
                </label>
            </div>

            <button type="submit" class="w-full btn-primary py-3 rounded-lg text-white font-semibold text-lg">
                <i class="fas fa-user-plus mr-2"></i>Cadastrar Grátis
            </button>
        </form>

        <div class="text-center mt-6">
            <p class="text-gray-300">
                Já tem uma conta?
                <a href="{{ route('login') }}" class="text-purple-400 hover:text-purple-300 font-semibold">
                    Faça login
                </a>
            </p>
        </div>
    </div>
</div>
@endsection
