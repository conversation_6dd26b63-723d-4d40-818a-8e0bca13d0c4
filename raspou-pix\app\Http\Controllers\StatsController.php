<?php

namespace App\Http\Controllers;

use App\Models\Game;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StatsController extends Controller
{
    public function realTimeStats()
    {
        $stats = [
            'total_players' => User::count(),
            'games_played_today' => Game::whereDate('created_at', today())->count(),
            'total_prizes_today' => Transaction::where('type', 'prize_win')
                ->whereDate('created_at', today())
                ->sum('amount'),
            'biggest_win_today' => Transaction::where('type', 'prize_win')
                ->whereDate('created_at', today())
                ->max('amount') ?? 0,
            'active_players' => User::where('updated_at', '>=', now()->subMinutes(30))->count(),
            'recent_winners' => Game::with(['user', 'scratchCard'])
                ->where('status', 'completed')
                ->where('prize_amount', '>', 0)
                ->orderBy('completed_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($game) {
                    return [
                        'player_name' => $game->user->name,
                        'prize' => number_format($game->prize_amount, 2, ',', '.'),
                        'game' => $game->scratchCard->name,
                        'time' => $game->completed_at->diffForHumans()
                    ];
                })
        ];

        return response()->json($stats);
    }

    public function userStats(Request $request)
    {
        $user = $request->user();
        
        $stats = [
            'total_games' => $user->games()->count(),
            'total_wins' => $user->games()->where('prize_amount', '>', 0)->count(),
            'total_spent' => $user->transactions()->where('type', 'game_purchase')->sum('amount'),
            'total_won' => $user->transactions()->where('type', 'prize_win')->sum('amount'),
            'biggest_win' => $user->transactions()->where('type', 'prize_win')->max('amount') ?? 0,
            'win_rate' => $user->games()->count() > 0 
                ? round(($user->games()->where('prize_amount', '>', 0)->count() / $user->games()->count()) * 100, 2)
                : 0,
            'current_balance' => $user->balance,
            'games_today' => $user->games()->whereDate('created_at', today())->count(),
            'wins_today' => $user->games()->whereDate('created_at', today())->where('prize_amount', '>', 0)->count(),
            'recent_games' => $user->games()->with('scratchCard')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($game) {
                    return [
                        'game_name' => $game->scratchCard->name,
                        'prize' => $game->prize_amount > 0 ? number_format($game->prize_amount, 2, ',', '.') : '0,00',
                        'status' => $game->status,
                        'time' => $game->created_at->diffForHumans(),
                        'won' => $game->prize_amount > 0
                    ];
                })
        ];

        return response()->json($stats);
    }

    public function leaderboard()
    {
        $topWinners = User::select('users.*')
            ->selectRaw('SUM(transactions.amount) as total_won')
            ->join('transactions', 'users.id', '=', 'transactions.user_id')
            ->where('transactions.type', 'prize_win')
            ->groupBy('users.id')
            ->orderBy('total_won', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($user, $index) {
                return [
                    'position' => $index + 1,
                    'name' => $user->name,
                    'total_won' => number_format($user->total_won, 2, ',', '.'),
                    'avatar' => $user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&background=random'
                ];
            });

        $topPlayers = User::select('users.*')
            ->selectRaw('COUNT(games.id) as total_games')
            ->join('games', 'users.id', '=', 'games.user_id')
            ->groupBy('users.id')
            ->orderBy('total_games', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($user, $index) {
                return [
                    'position' => $index + 1,
                    'name' => $user->name,
                    'total_games' => $user->total_games,
                    'avatar' => $user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&background=random'
                ];
            });

        return response()->json([
            'top_winners' => $topWinners,
            'top_players' => $topPlayers
        ]);
    }

    public function gameHistory(Request $request)
    {
        $user = $request->user();
        $page = $request->get('page', 1);
        $perPage = 20;

        $games = $user->games()
            ->with('scratchCard')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        $games->getCollection()->transform(function ($game) {
            return [
                'id' => $game->id,
                'game_name' => $game->scratchCard->name,
                'price' => number_format($game->scratchCard->price, 2, ',', '.'),
                'prize' => $game->prize_amount > 0 ? number_format($game->prize_amount, 2, ',', '.') : '0,00',
                'status' => $game->status,
                'created_at' => $game->created_at->format('d/m/Y H:i'),
                'completed_at' => $game->completed_at ? $game->completed_at->format('d/m/Y H:i') : null,
                'won' => $game->prize_amount > 0,
                'profit' => $game->prize_amount - $game->scratchCard->price
            ];
        });

        return response()->json($games);
    }
}
