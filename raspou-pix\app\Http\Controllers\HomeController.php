<?php

namespace App\Http\Controllers;

use App\Models\ScratchCard;
use App\Models\Game;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        $scratchCards = ScratchCard::active()
            ->orderBy('created_at', 'desc')
            ->take(8)
            ->get();

        // Buscar os últimos 3 ganhadores para exibir na home
        $recentWinners = Game::with(['user', 'scratchCard'])
            ->where('status', 'completed')
            ->where('prize_amount', '>', 0)
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        return view('home', compact('scratchCards', 'recentWinners'));
    }

    public function scratchCards()
    {
        $scratchCards = ScratchCard::active()
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('scratch-cards.index', compact('scratchCards'));
    }

    public function luckyNumbers()
    {
        return view('lucky-numbers.index');
    }

    public function winners()
    {
        // Buscar os últimos ganhadores dos jogos
        $winners = Game::with(['user', 'scratchCard'])
            ->where('status', 'completed')
            ->where('prize_amount', '>', 0)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('winners.index', compact('winners'));
    }

    public function wallet()
    {
        $user = auth()->user();
        $transactions = $user->transactions()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('wallet.index', compact('user', 'transactions'));
    }

    public function profile()
    {
        $user = auth()->user();
        $totalGames = $user->games()->count();
        $totalWins = $user->games()->where('prize_amount', '>', 0)->count();
        $totalPrizes = $user->games()->sum('prize_amount');

        return view('profile.index', compact('user', 'totalGames', 'totalWins', 'totalPrizes'));
    }
}
