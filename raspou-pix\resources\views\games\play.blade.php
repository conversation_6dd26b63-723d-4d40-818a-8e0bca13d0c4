@extends('layouts.app')

@section('title', 'Jogando - ' . $game->scratchCard->name)

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Game Header -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold mb-2">🎰 {{ $game->scratchCard->name }}</h1>
        <p class="text-gray-300 mb-4">{{ $game->scratchCard->description }}</p>
        <div class="flex justify-center space-x-8 text-lg">
            <div class="bg-gray-800 px-4 py-2 rounded-lg">
                <i class="fas fa-ticket-alt text-purple-400 mr-2"></i>
                Valor: <span class="font-semibold text-yellow-400">{{ $game->scratchCard->formatted_price }}</span>
            </div>
            <div class="bg-gray-800 px-4 py-2 rounded-lg">
                <i class="fas fa-wallet text-green-400 mr-2"></i>
                Saldo: <span class="font-semibold" id="user-balance">{{ auth()->user()->formatted_balance }}</span>
            </div>
        </div>
    </div>

    <!-- Game Status -->
    <div class="text-center mb-8">
        @if($game->status === 'playing')
            <div class="bg-blue-600 text-white px-6 py-3 rounded-lg inline-block">
                <i class="fas fa-play mr-2"></i>Clique nas posições para raspar!
            </div>
        @elseif($game->status === 'completed')
            @if($game->prize_amount > 0)
                <div class="bg-green-600 text-white px-6 py-3 rounded-lg inline-block">
                    <i class="fas fa-trophy mr-2"></i>Parabéns! Você ganhou {{ $game->formatted_prize }}!
                </div>
            @else
                <div class="bg-gray-600 text-white px-6 py-3 rounded-lg inline-block">
                    <i class="fas fa-times mr-2"></i>Que pena! Não foi desta vez.
                </div>
            @endif
        @endif
    </div>

    <!-- Scratch Card Game -->
    <div class="scratch-card p-8 max-w-md mx-auto mb-8">
        <div class="grid grid-cols-3 gap-4" id="scratch-grid">
            @for($i = 0; $i < 9; $i++)
                <div class="scratch-position h-20 flex items-center justify-center text-2xl font-bold cursor-pointer" 
                     data-position="{{ $i }}"
                     @if($game->game_data['scratched'][$i]) 
                         onclick="return false;" 
                     @else 
                         onclick="scratchPosition({{ $i }})" 
                     @endif>
                    @if($game->game_data['scratched'][$i])
                        <span class="text-4xl">{{ $game->game_data['positions'][$i] }}</span>
                    @else
                        <i class="fas fa-question text-gray-400"></i>
                    @endif
                </div>
            @endfor
        </div>
    </div>

    <!-- Game Instructions -->
    <div class="bg-gray-800 p-6 rounded-lg mb-8">
        <h3 class="text-xl font-semibold mb-4 text-center">
            <i class="fas fa-info-circle text-blue-400 mr-2"></i>Como Ganhar
        </h3>
        <div class="grid md:grid-cols-3 gap-4 text-center">
            <div>
                <div class="text-2xl mb-2">📏</div>
                <p class="text-sm text-gray-300">3 símbolos iguais em linha (horizontal)</p>
            </div>
            <div>
                <div class="text-2xl mb-2">📐</div>
                <p class="text-sm text-gray-300">3 símbolos iguais em coluna (vertical)</p>
            </div>
            <div>
                <div class="text-2xl mb-2">🔀</div>
                <p class="text-sm text-gray-300">3 símbolos iguais em diagonal</p>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="text-center space-x-4">
        <a href="{{ route('scratch-cards.index') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>Voltar às Raspadinhas
        </a>
        
        @if($game->status === 'completed')
            <a href="{{ route('home') }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
                <i class="fas fa-home mr-2"></i>Início
            </a>
        @endif
    </div>
</div>

<!-- Win Modal -->
<div id="winModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 p-8 rounded-lg max-w-md mx-4 text-center">
        <div class="text-6xl mb-4">🎉</div>
        <h2 class="text-2xl font-bold mb-4">Parabéns!</h2>
        <p class="text-lg mb-6">Você ganhou <span id="prize-amount" class="text-yellow-400 font-bold"></span>!</p>
        <p class="text-gray-300 mb-6">O valor foi creditado em sua conta automaticamente.</p>
        <button onclick="closeWinModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg">
            <i class="fas fa-check mr-2"></i>Continuar
        </button>
    </div>
</div>

@push('scripts')
<script>
let gameCompleted = {{ $game->status === 'completed' ? 'true' : 'false' }};

function scratchPosition(position) {
    if (gameCompleted) return;
    
    $.post(`/games/{{ $game->id }}/scratch`, {
        position: position,
        _token: $('meta[name="csrf-token"]').attr('content')
    })
    .done(function(response) {
        if (response.success) {
            // Update the scratched position
            const positionElement = $(`[data-position="${position}"]`);
            positionElement.html(`<span class="text-4xl">${response.symbol}</span>`);
            positionElement.addClass('scratched');
            positionElement.attr('onclick', 'return false;');
            
            // Check if game is completed
            if (response.all_scratched) {
                gameCompleted = true;
                
                if (response.prize_amount > 0) {
                    // Show win modal
                    $('#prize-amount').text('R$ ' + parseFloat(response.prize_amount).toFixed(2).replace('.', ','));
                    $('#winModal').removeClass('hidden').addClass('flex');
                    
                    // Update balance (simplified - in real app you'd fetch from server)
                    updateBalance();
                } else {
                    // Show lose message
                    setTimeout(() => {
                        alert('Que pena! Não foi desta vez. Tente novamente!');
                    }, 1000);
                }
            }
        }
    })
    .fail(function() {
        alert('Erro ao processar jogada. Tente novamente.');
    });
}

function closeWinModal() {
    $('#winModal').addClass('hidden').removeClass('flex');
}

function updateBalance() {
    // In a real application, you would fetch the updated balance from the server
    // For now, we'll just reload the page to get the updated balance
    setTimeout(() => {
        location.reload();
    }, 3000);
}

// Auto-reveal all positions if game is already completed
@if($game->status === 'completed')
$(document).ready(function() {
    $('.scratch-position').each(function(index) {
        if (!$(this).hasClass('scratched')) {
            $(this).html(`<span class="text-4xl">{{ $game->game_data['positions'][${index}] ?? '?' }}</span>`);
            $(this).addClass('scratched');
        }
    });
});
@endif
</script>
@endpush
@endsection
