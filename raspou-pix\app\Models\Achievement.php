<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Achievement extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'icon',
        'type',
        'requirement_value',
        'requirement_amount',
        'points',
        'is_active'
    ];

    protected $casts = [
        'requirement_amount' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_achievements')
                    ->withPivot('earned_at')
                    ->withTimestamps();
    }

    public function userAchievements()
    {
        return $this->hasMany(UserAchievement::class);
    }

    /**
     * Check if a user has earned this achievement
     */
    public function isEarnedBy(User $user): bool
    {
        return $this->users()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if user meets the requirements for this achievement
     */
    public function checkRequirements(User $user): bool
    {
        switch ($this->type) {
            case 'games_played':
                return $user->games()->count() >= $this->requirement_value;
                
            case 'total_wins':
                return $user->games()->where('prize_amount', '>', 0)->count() >= $this->requirement_value;
                
            case 'total_earned':
                return $user->transactions()
                    ->where('type', 'prize_win')
                    ->sum('amount') >= $this->requirement_amount;
                    
            case 'consecutive_wins':
                return $this->checkConsecutiveWins($user) >= $this->requirement_value;
                
            case 'big_win':
                return $user->transactions()
                    ->where('type', 'prize_win')
                    ->max('amount') >= $this->requirement_amount;
                    
            case 'daily_player':
                return $user->games()
                    ->whereDate('created_at', today())
                    ->count() >= $this->requirement_value;
                    
            default:
                return false;
        }
    }

    /**
     * Check consecutive wins for a user
     */
    private function checkConsecutiveWins(User $user): int
    {
        $games = $user->games()
            ->orderBy('completed_at', 'desc')
            ->get();

        $consecutiveWins = 0;
        $maxConsecutive = 0;

        foreach ($games as $game) {
            if ($game->prize_amount > 0) {
                $consecutiveWins++;
                $maxConsecutive = max($maxConsecutive, $consecutiveWins);
            } else {
                $consecutiveWins = 0;
            }
        }

        return $maxConsecutive;
    }

    /**
     * Award this achievement to a user
     */
    public function awardTo(User $user): bool
    {
        if ($this->isEarnedBy($user)) {
            return false; // Already earned
        }

        if (!$this->checkRequirements($user)) {
            return false; // Requirements not met
        }

        $this->users()->attach($user->id, ['earned_at' => now()]);
        
        // Add points to user (if you have a points system)
        // $user->increment('points', $this->points);

        return true;
    }
}
