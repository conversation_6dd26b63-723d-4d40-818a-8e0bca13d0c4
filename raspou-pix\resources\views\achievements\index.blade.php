@extends('layouts.app')

@section('title', 'Conquistas - Raspou Pix')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold mb-4">🏆 Suas Conquistas</h1>
        <p class="text-gray-300 text-lg">Acompanhe seu progresso e desbloqueie novas conquistas!</p>
    </div>

    @auth
    <!-- User Progress Summary -->
    <div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 mb-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div class="bg-white bg-opacity-20 rounded-lg p-4">
                <div class="text-2xl font-bold text-white" id="earned-achievements">-</div>
                <div class="text-sm text-gray-200">Conquistadas</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4">
                <div class="text-2xl font-bold text-white" id="total-achievements">-</div>
                <div class="text-sm text-gray-200">Total</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4">
                <div class="text-2xl font-bold text-white" id="total-points">-</div>
                <div class="text-sm text-gray-200">Pontos</div>
            </div>
            <div class="bg-white bg-opacity-20 rounded-lg p-4">
                <div class="text-2xl font-bold text-white" id="completion-rate">-%</div>
                <div class="text-sm text-gray-200">Progresso</div>
            </div>
        </div>
    </div>

    <!-- Achievement Categories -->
    <div class="space-y-8">
        <!-- Games Played -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-center text-blue-400">🎮 Jogos Jogados</h2>
            <div id="games-played-achievements" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse bg-gray-700 rounded-lg p-4 h-32"></div>
            </div>
        </div>

        <!-- Total Wins -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-center text-green-400">🏆 Vitórias</h2>
            <div id="total-wins-achievements" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse bg-gray-700 rounded-lg p-4 h-32"></div>
            </div>
        </div>

        <!-- Total Earned -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-center text-yellow-400">💰 Ganhos</h2>
            <div id="total-earned-achievements" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse bg-gray-700 rounded-lg p-4 h-32"></div>
            </div>
        </div>

        <!-- Consecutive Wins -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-center text-red-400">🔥 Sequências</h2>
            <div id="consecutive-wins-achievements" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse bg-gray-700 rounded-lg p-4 h-32"></div>
            </div>
        </div>

        <!-- Big Wins -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-center text-purple-400">💥 Grandes Prêmios</h2>
            <div id="big-win-achievements" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse bg-gray-700 rounded-lg p-4 h-32"></div>
            </div>
        </div>

        <!-- Daily Player -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-center text-orange-400">📅 Jogador Diário</h2>
            <div id="daily-player-achievements" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse bg-gray-700 rounded-lg p-4 h-32"></div>
            </div>
        </div>

        <!-- Special -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-center text-pink-400">🌟 Especiais</h2>
            <div id="special-achievements" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Loading placeholder -->
                <div class="animate-pulse bg-gray-700 rounded-lg p-4 h-32"></div>
            </div>
        </div>
    </div>
    @else
    <div class="text-center py-16">
        <div class="text-6xl mb-4">🔒</div>
        <h2 class="text-2xl font-bold mb-4">Faça login para ver suas conquistas</h2>
        <p class="text-gray-300 mb-8">Cadastre-se e comece a desbloquear conquistas incríveis!</p>
        <a href="{{ route('login') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg transition-colors">
            Fazer Login
        </a>
    </div>
    @endauth
</div>

@push('scripts')
<script>
@auth
class AchievementsManager {
    constructor() {
        this.init();
    }

    async init() {
        await this.loadAchievements();
    }

    async loadAchievements() {
        try {
            const response = await fetch('/api/achievements');
            const data = await response.json();
            
            this.renderSummary(data.summary);
            this.renderAchievementsByType(data.achievements);
        } catch (error) {
            console.error('Error loading achievements:', error);
        }
    }

    renderSummary(summary) {
        document.getElementById('earned-achievements').textContent = summary.earned;
        document.getElementById('total-achievements').textContent = summary.total;
        document.getElementById('total-points').textContent = summary.points;
        document.getElementById('completion-rate').textContent = summary.completion_rate + '%';
    }

    renderAchievementsByType(achievements) {
        const types = ['games_played', 'total_wins', 'total_earned', 'consecutive_wins', 'big_win', 'daily_player', 'special'];
        
        types.forEach(type => {
            const container = document.getElementById(`${type.replace('_', '-')}-achievements`);
            const typeAchievements = achievements.filter(a => a.type === type);
            
            if (typeAchievements.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center col-span-full">Nenhuma conquista nesta categoria</p>';
                return;
            }

            container.innerHTML = typeAchievements.map(achievement => this.renderAchievement(achievement)).join('');
        });
    }

    renderAchievement(achievement) {
        const isEarned = achievement.is_earned;
        const earnedClass = isEarned ? 'bg-gradient-to-r from-green-600 to-blue-600' : 'bg-gray-700';
        const textClass = isEarned ? 'text-white' : 'text-gray-400';
        const iconClass = isEarned ? '' : 'grayscale opacity-50';

        return `
            <div class="${earnedClass} rounded-lg p-4 text-center transition-all hover:scale-105">
                <div class="text-4xl mb-2 ${iconClass}">${achievement.icon}</div>
                <h3 class="font-bold ${textClass} mb-1">${achievement.name}</h3>
                <p class="text-sm ${textClass} mb-2">${achievement.description}</p>
                <div class="text-xs ${textClass}">
                    ${achievement.points} pontos
                    ${isEarned ? `<br><span class="text-green-300">✓ Conquistada ${achievement.earned_at}</span>` : '<br><span class="text-gray-500">🔒 Bloqueada</span>'}
                </div>
            </div>
        `;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new AchievementsManager();
});
@endauth
</script>
@endpush
@endsection
