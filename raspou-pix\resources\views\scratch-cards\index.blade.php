@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON><PERSON><PERSON> Disponíveis - <PERSON><PERSON><PERSON> Pix')

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">🎫 Raspadinhas Disponíveis</h1>
        <p class="text-xl text-gray-300">Escolha sua raspadinha e tente a sorte!</p>
    </div>

    @auth
    <!-- User Balance -->
    <div class="bg-gray-800 p-6 rounded-lg mb-8 text-center">
        <div class="flex justify-center items-center space-x-8">
            <div class="balance-card px-6 py-3 rounded-lg">
                <i class="fas fa-wallet text-2xl mr-3"></i>
                <span class="text-lg">Saldo Atual: </span>
                <span class="text-2xl font-bold">{{ auth()->user()->formatted_balance }}</span>
            </div>
            <div class="space-x-4">
                <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>Depositar
                </button>
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-minus mr-2"></i>Sacar
                </button>
            </div>
        </div>
    </div>
    @endauth

    <!-- Scratch Cards Grid -->
    @if($scratchCards->count() > 0)
        <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            @foreach($scratchCards as $card)
            <div class="scratch-card p-6 text-center transform hover:scale-105 transition-transform">
                <div class="text-5xl mb-4">🎰</div>

                <h3 class="text-xl font-semibold mb-3">{{ $card->name }}</h3>

                <p class="text-gray-200 text-sm mb-4 h-12 overflow-hidden">
                    {{ $card->description }}
                </p>

                <div class="text-3xl font-bold text-yellow-400 mb-4">
                    {{ $card->formatted_price }}
                </div>

                <!-- Prize Structure Preview -->
                <div class="bg-gray-700 p-3 rounded-lg mb-4">
                    <div class="text-xs text-gray-300 mb-2">Prêmios Possíveis:</div>
                    <div class="space-y-1">
                        @foreach(collect($card->prize_structure)->sortByDesc('amount')->take(3) as $prize)
                            @if($prize['amount'] > 0)
                                <div class="flex justify-between text-xs">
                                    <span>R$ {{ number_format($prize['amount'], 2, ',', '.') }}</span>
                                    <span class="text-gray-400">{{ number_format(($prize['weight'] / array_sum(array_column($card->prize_structure, 'weight'))) * 100, 2) }}%</span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <div class="text-sm text-gray-300 mb-4 flex items-center justify-center">
                    <i class="fas fa-ticket-alt mr-2"></i>
                    <span class="font-semibold">{{ $card->available_cards }}</span> disponíveis
                </div>

                @if($card->available_cards > 0)
                    @auth
                        @if(auth()->user()->balance >= $card->price)
                            <button onclick="purchaseCard({{ $card->id }})"
                                    class="w-full bg-yellow-500 hover:bg-yellow-400 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors">
                                <i class="fas fa-shopping-cart mr-2"></i>Comprar Agora
                            </button>
                        @else
                            <button disabled class="w-full bg-gray-600 text-gray-400 font-semibold py-3 px-4 rounded-lg cursor-not-allowed">
                                <i class="fas fa-wallet mr-2"></i>Saldo Insuficiente
                            </button>
                        @endif
                    @else
                        <a href="{{ route('login') }}"
                           class="block w-full bg-yellow-500 hover:bg-yellow-400 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-sign-in-alt mr-2"></i>Entrar para Jogar
                        </a>
                    @endauth
                @else
                    <button disabled class="w-full bg-red-600 text-white font-semibold py-3 px-4 rounded-lg cursor-not-allowed">
                        <i class="fas fa-times mr-2"></i>Esgotado
                    </button>
                @endif
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="flex justify-center">
            {{ $scratchCards->links() }}
        </div>
    @else
        <div class="text-center py-16">
            <div class="text-6xl mb-4">😔</div>
            <h2 class="text-2xl font-bold mb-4">Nenhuma raspadinha disponível</h2>
            <p class="text-gray-300 mb-8">No momento não temos raspadinhas disponíveis. Volte em breve!</p>
            <a href="{{ route('home') }}" class="btn-primary px-6 py-3 rounded-lg text-white font-semibold">
                <i class="fas fa-home mr-2"></i>Voltar ao Início
            </a>
        </div>
    @endif
</div>

@auth
@push('scripts')
<script>
function purchaseCard(cardId) {
    if (confirm('Deseja comprar esta raspadinha?')) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Comprando...';
        button.disabled = true;

        $.post(`/games/purchase/${cardId}`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
        .done(function(response) {
            if (response.success) {
                window.location.href = response.redirect;
            } else {
                alert(response.message);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .fail(function() {
            alert('Erro ao processar compra. Tente novamente.');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}
</script>
@endpush
@endauth
@endsection
