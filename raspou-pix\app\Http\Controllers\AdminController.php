<?php

namespace App\Http\Controllers;

use App\Models\Game;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Add admin middleware if you have one
        // $this->middleware('admin');
    }

    public function dashboard()
    {
        // Get statistics
        $stats = $this->getDashboardStats();
        
        return view('admin.dashboard', compact('stats'));
    }

    public function users()
    {
        $users = User::withCount('games')
            ->with(['games' => function($query) {
                $query->select('user_id', DB::raw('SUM(prize_amount) as total_winnings'))
                      ->groupBy('user_id');
            }])
            ->paginate(20);

        return view('admin.users', compact('users'));
    }

    public function games()
    {
        $games = Game::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        return view('admin.games', compact('games'));
    }

    public function settings()
    {
        return view('admin.settings');
    }

    public function reports()
    {
        $dailyStats = $this->getDailyStats();
        $monthlyStats = $this->getMonthlyStats();
        
        return view('admin.reports', compact('dailyStats', 'monthlyStats'));
    }

    private function getDashboardStats()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        return [
            'total_profit' => Game::sum('cost') - Game::sum('prize_amount'),
            'total_deposits' => Game::sum('cost'),
            'total_withdrawals' => Game::sum('prize_amount'),
            'total_users' => User::count(),
            'total_games' => Game::count(),
            'games_today' => Game::whereDate('created_at', $today)->count(),
            'profit_today' => Game::whereDate('created_at', $today)->sum('cost') - 
                             Game::whereDate('created_at', $today)->sum('prize_amount'),
            'deposits_today' => Game::whereDate('created_at', $today)->sum('cost'),
            'withdrawals_today' => Game::whereDate('created_at', $today)->sum('prize_amount'),
            'users_today' => User::whereDate('created_at', $today)->count(),
            'games_this_month' => Game::where('created_at', '>=', $thisMonth)->count(),
            'profit_this_month' => Game::where('created_at', '>=', $thisMonth)->sum('cost') - 
                                  Game::where('created_at', '>=', $thisMonth)->sum('prize_amount'),
        ];
    }

    private function getDailyStats()
    {
        return Game::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as games_count'),
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('SUM(prize_amount) as total_prizes'),
                DB::raw('SUM(cost) - SUM(prize_amount) as profit')
            )
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date', 'desc')
            ->get();
    }

    private function getMonthlyStats()
    {
        return Game::select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as games_count'),
                DB::raw('SUM(cost) as total_cost'),
                DB::raw('SUM(prize_amount) as total_prizes'),
                DB::raw('SUM(cost) - SUM(prize_amount) as profit')
            )
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->groupBy(DB::raw('YEAR(created_at)'), DB::raw('MONTH(created_at)'))
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
    }

    public function updateGameSettings(Request $request)
    {
        $request->validate([
            'game_cost' => 'required|numeric|min:0.01',
            'max_prize' => 'required|numeric|min:0',
            'win_probability' => 'required|numeric|min:0|max:100',
        ]);

        // Store settings in config or database
        // For now, we'll use session
        session([
            'game_cost' => $request->game_cost,
            'max_prize' => $request->max_prize,
            'win_probability' => $request->win_probability,
        ]);

        return redirect()->back()->with('success', 'Configurações atualizadas com sucesso!');
    }

    public function deleteGame($id)
    {
        $game = Game::findOrFail($id);
        $game->delete();

        return redirect()->back()->with('success', 'Jogo deletado com sucesso!');
    }

    public function blockUser($id)
    {
        $user = User::findOrFail($id);
        $user->update(['is_blocked' => true]);

        return redirect()->back()->with('success', 'Usuário bloqueado com sucesso!');
    }

    public function unblockUser($id)
    {
        $user = User::findOrFail($id);
        $user->update(['is_blocked' => false]);

        return redirect()->back()->with('success', 'Usuário desbloqueado com sucesso!');
    }
}
