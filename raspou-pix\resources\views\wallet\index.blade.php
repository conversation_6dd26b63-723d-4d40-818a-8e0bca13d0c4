@extends('layouts.app')

@section('title', '<PERSON><PERSON> - <PERSON><PERSON><PERSON> Pi<PERSON>')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">
            <i class="fas fa-wallet mr-3 text-green-500"></i>
            <PERSON><PERSON>
        </h1>
        <p class="text-xl text-gray-300">
            Gerencie seu saldo e acompanhe suas transações
        </p>
    </div>

    <!-- Saldo Atual -->
    <div class="max-w-4xl mx-auto mb-8">
        <div class="card p-8 text-center bg-gradient-to-r from-green-600 to-blue-600">
            <h2 class="text-2xl font-bold text-white mb-4">Saldo Atual</h2>
            <p class="text-5xl font-bold text-white mb-4">
                R$ {{ number_format($user->balance, 2, ',', '.') }}
            </p>
            <div class="flex justify-center space-x-4 mt-6">
                <button class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Depositar
                </button>
                <button class="btn-secondary">
                    <i class="fas fa-minus mr-2"></i>Sacar
                </button>
            </div>
        </div>
    </div>

    <!-- Estatísticas Rápidas -->
    <div class="max-w-6xl mx-auto mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="card p-6 text-center">
                <i class="fas fa-arrow-up text-3xl text-green-500 mb-3"></i>
                <h3 class="text-lg font-semibold text-white mb-2">Depósitos</h3>
                <p class="text-2xl font-bold text-green-500">
                    R$ {{ number_format($transactions->where('type', 'deposit')->sum('amount'), 2, ',', '.') }}
                </p>
            </div>
            <div class="card p-6 text-center">
                <i class="fas fa-arrow-down text-3xl text-red-500 mb-3"></i>
                <h3 class="text-lg font-semibold text-white mb-2">Gastos</h3>
                <p class="text-2xl font-bold text-red-500">
                    R$ {{ number_format($transactions->where('type', 'game_purchase')->sum('amount'), 2, ',', '.') }}
                </p>
            </div>
            <div class="card p-6 text-center">
                <i class="fas fa-trophy text-3xl text-yellow-500 mb-3"></i>
                <h3 class="text-lg font-semibold text-white mb-2">Prêmios</h3>
                <p class="text-2xl font-bold text-yellow-500">
                    R$ {{ number_format($transactions->where('type', 'prize_win')->sum('amount'), 2, ',', '.') }}
                </p>
            </div>
            <div class="card p-6 text-center">
                <i class="fas fa-exchange-alt text-3xl text-blue-500 mb-3"></i>
                <h3 class="text-lg font-semibold text-white mb-2">Transações</h3>
                <p class="text-2xl font-bold text-blue-500">{{ $transactions->count() }}</p>
            </div>
        </div>
    </div>

    <!-- Histórico de Transações -->
    <div class="max-w-6xl mx-auto">
        <div class="card p-6">
            <h2 class="text-2xl font-bold text-white mb-6">
                <i class="fas fa-history mr-2"></i>Histórico de Transações
            </h2>

            @if($transactions->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="pb-3 text-gray-400 font-semibold">Data</th>
                                <th class="pb-3 text-gray-400 font-semibold">Tipo</th>
                                <th class="pb-3 text-gray-400 font-semibold">Descrição</th>
                                <th class="pb-3 text-gray-400 font-semibold">Valor</th>
                                <th class="pb-3 text-gray-400 font-semibold">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($transactions as $transaction)
                            <tr class="border-b border-gray-800 hover:bg-gray-800 transition-colors">
                                <td class="py-4 text-gray-300">
                                    {{ $transaction->created_at->format('d/m/Y H:i') }}
                                </td>
                                <td class="py-4">
                                    @if($transaction->type === 'deposit')
                                        <span class="badge bg-green-600 text-white">
                                            <i class="fas fa-arrow-up mr-1"></i>Depósito
                                        </span>
                                    @elseif($transaction->type === 'game_purchase')
                                        <span class="badge bg-red-600 text-white">
                                            <i class="fas fa-shopping-cart mr-1"></i>Compra
                                        </span>
                                    @elseif($transaction->type === 'prize_win')
                                        <span class="badge bg-yellow-600 text-white">
                                            <i class="fas fa-trophy mr-1"></i>Prêmio
                                        </span>
                                    @elseif($transaction->type === 'withdrawal')
                                        <span class="badge bg-blue-600 text-white">
                                            <i class="fas fa-arrow-down mr-1"></i>Saque
                                        </span>
                                    @endif
                                </td>
                                <td class="py-4 text-gray-300">
                                    {{ $transaction->description }}
                                </td>
                                <td class="py-4">
                                    @if(in_array($transaction->type, ['deposit', 'prize_win']))
                                        <span class="text-green-500 font-bold">
                                            +R$ {{ number_format($transaction->amount, 2, ',', '.') }}
                                        </span>
                                    @else
                                        <span class="text-red-500 font-bold">
                                            -R$ {{ number_format($transaction->amount, 2, ',', '.') }}
                                        </span>
                                    @endif
                                </td>
                                <td class="py-4">
                                    @if($transaction->status === 'completed')
                                        <span class="badge bg-green-600 text-white">
                                            <i class="fas fa-check mr-1"></i>Concluído
                                        </span>
                                    @elseif($transaction->status === 'pending')
                                        <span class="badge bg-yellow-600 text-white">
                                            <i class="fas fa-clock mr-1"></i>Pendente
                                        </span>
                                    @elseif($transaction->status === 'failed')
                                        <span class="badge bg-red-600 text-white">
                                            <i class="fas fa-times mr-1"></i>Falhou
                                        </span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                <div class="mt-6">
                    {{ $transactions->links() }}
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-receipt text-6xl text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2">Nenhuma transação ainda</h3>
                    <p class="text-gray-400 mb-4">Suas transações aparecerão aqui quando você começar a jogar.</p>
                    <a href="{{ route('scratch-cards.index') }}" class="btn-primary">
                        <i class="fas fa-play mr-2"></i>Começar a Jogar
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
